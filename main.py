# 版本号: v2.0.0
# 变更记录:
# - v1.0.0: 初始版本，实现完整的自动化导出流程
# - v1.1.0: 添加自动登录功能，支持cookie自动登录
# - v2.0.0: 整合多功能模块，创建统一主程序入口，提升用户体验

"""
统一主程序入口
整合自动化数据导出工具和月份URL生成器等功能模块
"""

import asyncio
import sys
from typing import List, Tuple, Optional

from time_generator import TimeRangeGenerator
from browser_controller import BrowserController
from export_handler import ExportHandler
from month_url_generator import MonthUrlGenerator, run_interactive_mode as run_month_url_generator
from utils import setup_logger, print_colored, format_time_range
import config

logger = setup_logger(__name__)


class MainApplication:
    """统一主程序应用类"""

    def __init__(self):
        """初始化主程序应用"""
        self.auto_export_tool = None
        self.month_url_generator = MonthUrlGenerator()
        self.logger = logger

    def display_main_menu(self):
        """显示主菜单"""
        print_colored("\n" + "=" * 70, "cyan")
        print_colored("🚀 数据处理工具集 v2.0.0", "cyan")
        print_colored("=" * 70, "cyan")
        print_colored("请选择要使用的功能:", "white")
        print_colored("", "white")
        print_colored("1. 📊 自动化数据导出工具", "blue")
        print_colored("   - 绕过网站导出限制，自动导出全年数据", "white")
        print_colored("   - 支持自动登录和Cookie管理", "white")
        print_colored("", "white")
        print_colored("2. 🔗 月份URL生成器", "green")
        print_colored("   - 根据月份输入生成特定URL", "white")
        print_colored("   - 支持单个或多个连续月份", "white")
        print_colored("", "white")
        print_colored("3. ⚙️  Cookie管理工具", "yellow")
        print_colored("   - 提取、测试和管理登录Cookie", "white")
        print_colored("   - 支持自动登录配置", "white")
        print_colored("", "white")
        print_colored("4. ❓ 帮助信息", "magenta")
        print_colored("   - 查看各功能模块的详细说明", "white")
        print_colored("", "white")
        print_colored("0. 🚪 退出程序", "red")
        print_colored("=" * 70, "cyan")

    def display_help_info(self):
        """显示帮助信息"""
        print_colored("\n" + "=" * 70, "cyan")
        print_colored("📖 功能模块详细说明", "cyan")
        print_colored("=" * 70, "cyan")

        print_colored("\n📊 自动化数据导出工具:", "blue")
        print_colored("  • 功能: 绕过网站每次只能导出3个月数据的限制", "white")
        print_colored("  • 原理: 按1个月为单位自动分割时间范围，逐个导出", "white")
        print_colored("  • 特性: 支持自动登录、智能重试、详细日志", "white")
        print_colored("  • 使用: 需要先启动Chrome浏览器并开启调试模式", "white")

        print_colored("\n🔗 月份URL生成器:", "green")
        print_colored("  • 功能: 根据月份数字生成带日期参数的URL", "white")
        print_colored("  • 输入: 支持1-12的数字，可单个或多个连续月份", "white")
        print_colored("  • 格式: 支持逗号分隔(1,2,3)或空格分隔(1 2 3)", "white")
        print_colored("  • 限制: 最多3个连续月份，自动计算日期范围", "white")

        print_colored("\n⚙️  Cookie管理工具:", "yellow")
        print_colored("  • 提取: 从已登录浏览器中提取Cookie", "white")
        print_colored("  • 测试: 验证保存的Cookie是否有效", "white")
        print_colored("  • 管理: 查看、删除已保存的Cookie信息", "white")
        print_colored("  • 自动登录: 配置后可实现免手动登录", "white")

        print_colored("\n💡 使用建议:", "magenta")
        print_colored("  • 首次使用建议先运行Cookie管理工具配置自动登录", "white")
        print_colored("  • 自动化导出前确保网络稳定，避免中途断网", "white")
        print_colored("  • 月份URL生成器可用于快速生成测试链接", "white")
        print_colored("  • 遇到问题可查看日志文件获取详细错误信息", "white")

        print_colored("=" * 70, "cyan")

    async def run_auto_export_tool(self):
        """运行自动化数据导出工具"""
        print_colored("\n🚀 启动自动化数据导出工具...", "blue")

        if self.auto_export_tool is None:
            self.auto_export_tool = AutoExportTool()

        try:
            # 初始化工具
            if not await self.auto_export_tool.initialize():
                print_colored("❌ 自动化导出工具初始化失败", "red")
                return False

            # 运行导出流程
            success = await self.auto_export_tool.run_export_process()

            if success:
                print_colored("\n🎉 所有数据导出完成！", "green")
            else:
                print_colored("\n⚠️ 部分数据导出失败，请检查失败报告", "yellow")

            return success

        except KeyboardInterrupt:
            print_colored("\n⚠️ 用户中断操作", "yellow")
            return False
        except Exception as e:
            print_colored(f"\n❌ 导出工具运行异常: {e}", "red")
            self.logger.error(f"导出工具运行异常: {e}")
            return False

    def run_month_url_generator(self):
        """运行月份URL生成器"""
        print_colored("\n🔗 启动月份URL生成器...", "green")
        try:
            run_month_url_generator()
        except KeyboardInterrupt:
            print_colored("\n⚠️ 用户中断操作", "yellow")
        except Exception as e:
            print_colored(f"\n❌ 月份URL生成器运行异常: {e}", "red")
            self.logger.error(f"月份URL生成器运行异常: {e}")

    def run_cookie_management(self):
        """运行Cookie管理工具"""
        print_colored("\n⚙️  启动Cookie管理工具...", "yellow")
        print_colored("请选择Cookie管理操作:", "white")
        print_colored("1. 提取Cookie (从已登录浏览器)", "blue")
        print_colored("2. 测试自动登录", "green")
        print_colored("3. 查看Cookie信息", "cyan")
        print_colored("4. 删除保存的Cookie", "red")
        print_colored("0. 返回主菜单", "white")

        while True:
            try:
                choice = input("\n请选择操作 (0-4): ").strip()

                if choice == '0':
                    break
                elif choice == '1':
                    self._extract_cookies()
                elif choice == '2':
                    self._test_auto_login()
                elif choice == '3':
                    self._show_cookie_info()
                elif choice == '4':
                    self._delete_cookies()
                else:
                    print_colored("❌ 无效选择，请输入0-4之间的数字", "red")

            except KeyboardInterrupt:
                print_colored("\n⚠️ 返回主菜单", "yellow")
                break
            except Exception as e:
                print_colored(f"❌ Cookie管理操作异常: {e}", "red")

    def _extract_cookies(self):
        """提取Cookie"""
        print_colored("🔄 正在提取Cookie...", "blue")
        try:
            import subprocess
            result = subprocess.run([sys.executable, "extract_cookies.py"],
                                  capture_output=True, text=True, cwd=".")
            if result.returncode == 0:
                print_colored("✅ Cookie提取完成", "green")
                if result.stdout:
                    print(result.stdout)
            else:
                print_colored("❌ Cookie提取失败", "red")
                if result.stderr:
                    print(result.stderr)
        except Exception as e:
            print_colored(f"❌ Cookie提取异常: {e}", "red")

    def _test_auto_login(self):
        """测试自动登录"""
        print_colored("🔄 正在测试自动登录...", "blue")
        try:
            import subprocess
            result = subprocess.run([sys.executable, "test_auto_login.py"],
                                  capture_output=True, text=True, cwd=".")
            if result.returncode == 0:
                print_colored("✅ 自动登录测试完成", "green")
                if result.stdout:
                    print(result.stdout)
            else:
                print_colored("❌ 自动登录测试失败", "red")
                if result.stderr:
                    print(result.stderr)
        except Exception as e:
            print_colored(f"❌ 自动登录测试异常: {e}", "red")

    def _show_cookie_info(self):
        """显示Cookie信息"""
        print_colored("🔄 正在查看Cookie信息...", "blue")
        try:
            import subprocess
            result = subprocess.run([sys.executable, "cookie_cli.py", "info"],
                                  capture_output=True, text=True, cwd=".")
            if result.returncode == 0:
                if result.stdout:
                    print(result.stdout)
            else:
                print_colored("❌ 查看Cookie信息失败", "red")
                if result.stderr:
                    print(result.stderr)
        except Exception as e:
            print_colored(f"❌ 查看Cookie信息异常: {e}", "red")

    def _delete_cookies(self):
        """删除Cookie"""
        print_colored("⚠️  确认要删除保存的Cookie吗？(y/N): ", "yellow", end="")
        confirm = input().strip().lower()
        if confirm in ['y', 'yes', '是']:
            try:
                import subprocess
                result = subprocess.run([sys.executable, "cookie_cli.py", "delete"],
                                      capture_output=True, text=True, cwd=".")
                if result.returncode == 0:
                    print_colored("✅ Cookie删除完成", "green")
                    if result.stdout:
                        print(result.stdout)
                else:
                    print_colored("❌ Cookie删除失败", "red")
                    if result.stderr:
                        print(result.stderr)
            except Exception as e:
                print_colored(f"❌ Cookie删除异常: {e}", "red")
        else:
            print_colored("❌ 操作已取消", "yellow")

    async def run(self):
        """运行主程序"""
        print_colored("🎉 欢迎使用数据处理工具集！", "green")

        while True:
            try:
                self.display_main_menu()
                choice = input("\n请选择功能 (0-4): ").strip()

                if choice == '0' or choice.lower() in ['quit', 'exit', '退出']:
                    print_colored("\n👋 感谢使用，再见！", "green")
                    break
                elif choice == '1':
                    await self.run_auto_export_tool()
                elif choice == '2':
                    self.run_month_url_generator()
                elif choice == '3':
                    self.run_cookie_management()
                elif choice == '4':
                    self.display_help_info()
                else:
                    print_colored("❌ 无效选择，请输入0-4之间的数字", "red")

                # 功能执行完毕后的提示
                if choice in ['1', '2', '3']:
                    print_colored("\n按回车键返回主菜单...", "cyan")
                    input()

            except KeyboardInterrupt:
                print_colored("\n\n👋 程序被用户中断，再见！", "yellow")
                break
            except Exception as e:
                print_colored(f"\n❌ 程序运行异常: {e}", "red")
                self.logger.error(f"程序运行异常: {e}")
                print_colored("按回车键继续...", "cyan")
                input()

    async def cleanup(self):
        """清理资源"""
        try:
            if self.auto_export_tool:
                await self.auto_export_tool.cleanup()
        except Exception as e:
            self.logger.error(f"清理资源时出错: {e}")


class AutoExportTool:
    """自动化导出工具主类"""
    
    def __init__(self):
        """初始化自动化导出工具"""
        self.time_generator = TimeRangeGenerator(
            year=config.EXPORT_YEAR,
            months_per_range=config.MONTHS_PER_EXPORT
        )
        self.browser_controller = BrowserController()
        self.export_handler = None
        self.logger = logger
        
    async def initialize(self) -> bool:
        """
        初始化工具
        
        Returns:
            初始化是否成功
        """
        try:
            print_colored("=== 自动化数据导出工具 ===", "cyan")
            print_colored(f"目标年份: {config.EXPORT_YEAR}", "blue")
            print_colored(f"每次导出: {config.MONTHS_PER_EXPORT}个月", "blue")
            print_colored(f"目标网址: {config.TARGET_URL}", "blue")
            print_colored("=" * 50, "cyan")
            
            # 连接到Chrome浏览器
            if not await self.browser_controller.connect_to_existing_chrome():
                print_colored("请确保Chrome浏览器已启动并开启远程调试模式", "red")
                print_colored("启动命令: chrome.exe --remote-debugging-port=9222", "yellow")
                return False

            # 尝试自动登录
            print_colored("尝试自动登录...", "blue")
            auto_login_success = await self.browser_controller.auto_login_with_cookies()

            if auto_login_success:
                print_colored("✓ 自动登录成功", "green")
            else:
                print_colored("✗ 自动登录失败，需要手动登录", "yellow")
                print_colored("请在浏览器中完成登录，然后按回车继续...", "yellow")
                input()

                # 手动登录后，导航到目标页面
                if not await self.browser_controller.navigate_to_target_page(config.TARGET_URL):
                    return False

                # 询问是否保存cookies
                print_colored("是否保存当前登录状态的cookies？(Y/n): ", "yellow", end="")
                save_cookies = input().strip().lower()
                if save_cookies not in ['n', 'no', '否']:
                    cookie_saved = await self.browser_controller.extract_and_save_cookies()
                    if cookie_saved:
                        print_colored("✓ Cookies已保存，下次可以自动登录", "green")
                    else:
                        print_colored("✗ Cookies保存失败", "red")
            
            # 初始化导出处理器
            self.export_handler = ExportHandler(self.browser_controller)
            
            print_colored("✓ 工具初始化完成", "green")
            return True
            
        except Exception as e:
            print_colored(f"✗ 初始化失败: {e}", "red")
            self.logger.error(f"初始化失败: {e}")
            return False
    
    async def run_export_process(self) -> bool:
        """
        运行完整的导出流程
        
        Returns:
            导出流程是否成功完成
        """
        try:
            # 生成时间范围列表
            time_ranges = self.time_generator.generate_time_ranges()
            total_ranges = len(time_ranges)
            
            print_colored(f"\n生成了 {total_ranges} 个时间范围:", "blue")
            for i, (start, end) in enumerate(time_ranges, 1):
                description = self.time_generator.get_range_description(start, end)
                print_colored(f"  {i}. {description} ({start} 至 {end})", "white")
            
            # 确认是否继续
            print_colored(f"\n准备开始导出，预计需要 {total_ranges * 2} 分钟", "yellow")
            user_input = input("是否继续？(y/N): ").strip().lower()
            if user_input not in ['y', 'yes', '是']:
                print_colored("用户取消操作", "yellow")
                return False
            
            # 逐个处理时间范围
            successful_exports = 0
            failed_exports = []
            
            for i, (start_date, end_date) in enumerate(time_ranges, 1):
                print_colored(f"\n[{i}/{total_ranges}] 处理时间范围", "cyan")
                
                # 验证日期范围
                if not self.time_generator.validate_date_range(start_date, end_date):
                    print_colored(f"✗ 跳过无效的日期范围: {start_date} 至 {end_date}", "red")
                    failed_exports.append((start_date, end_date, "无效日期范围"))
                    continue
                
                # 执行导出
                retry_count = 0
                export_success = False
                
                while retry_count < config.MAX_RETRIES and not export_success:
                    if retry_count > 0:
                        print_colored(f"第 {retry_count + 1} 次重试...", "yellow")
                        await asyncio.sleep(config.RETRY_DELAY)
                    
                    export_success = await self.export_handler.export_data_for_range(
                        start_date, end_date
                    )
                    
                    if not export_success:
                        retry_count += 1
                
                if export_success:
                    successful_exports += 1
                    print_colored(f"✓ [{i}/{total_ranges}] 导出成功", "green")
                else:
                    failed_exports.append((start_date, end_date, "导出失败"))
                    print_colored(f"✗ [{i}/{total_ranges}] 导出失败", "red")
                
                # 进度报告
                print_colored(f"进度: {i}/{total_ranges} ({i/total_ranges*100:.1f}%)", "blue")
                
                # 如果不是最后一个，等待一段时间
                if i < total_ranges:
                    print_colored("等待下一个导出...", "yellow")
                    await asyncio.sleep(config.RETRY_DELAY)
            
            # 最终报告
            self._print_final_report(successful_exports, failed_exports, total_ranges)
            
            return len(failed_exports) == 0
            
        except Exception as e:
            print_colored(f"✗ 导出流程出错: {e}", "red")
            self.logger.error(f"导出流程出错: {e}")
            return False
    
    def _print_final_report(self, successful: int, failed: List[Tuple], total: int):
        """打印最终报告"""
        print_colored("\n" + "=" * 50, "cyan")
        print_colored("导出完成报告", "cyan")
        print_colored("=" * 50, "cyan")
        
        print_colored(f"总计时间范围: {total}", "blue")
        print_colored(f"成功导出: {successful}", "green")
        print_colored(f"失败导出: {len(failed)}", "red")
        print_colored(f"成功率: {successful/total*100:.1f}%", "blue")
        
        if failed:
            print_colored("\n失败的时间范围:", "red")
            for start, end, reason in failed:
                print_colored(f"  • {start} 至 {end} - {reason}", "red")
        
        print_colored("=" * 50, "cyan")
    
    async def cleanup(self):
        """清理资源"""
        try:
            if self.browser_controller:
                await self.browser_controller.close()
        except Exception as e:
            self.logger.error(f"清理资源时出错: {e}")

async def main():
    """主函数"""
    app = MainApplication()

    try:
        # 运行主程序
        await app.run()
        return 0

    except KeyboardInterrupt:
        print_colored("\n👋 程序被用户中断，再见！", "yellow")
        return 1
    except Exception as e:
        print_colored(f"\n❌ 程序异常退出: {e}", "red")
        logger.error(f"程序异常退出: {e}")
        return 1
    finally:
        await app.cleanup()

if __name__ == "__main__":
    # 运行统一主程序
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
