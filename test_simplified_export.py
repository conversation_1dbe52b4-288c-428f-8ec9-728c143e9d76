# 版本号: v1.0.0
# 变更记录:
# - v1.0.0: 初始版本，测试简化导出工具的功能

"""
简化导出工具测试脚本
用于测试URL生成和基本功能
"""

import asyncio
from simplified_export import SimplifiedExportTool
from browser_controller import BrowserController
from utils import print_colored


def test_url_generation():
    """测试URL生成功能"""
    print_colored("🧪 测试URL生成功能", "cyan")
    print_colored("=" * 50, "cyan")
    
    # 创建一个模拟的浏览器控制器（仅用于测试）
    browser_controller = None
    tool = SimplifiedExportTool(browser_controller)
    
    # 测试单个月份
    print_colored("\n📅 测试单个月份 (1月):", "blue")
    urls = tool.generate_monthly_urls(1, 1)
    for url, month in urls:
        print_colored(f"  {month}月: {url}", "white")
    
    # 测试多个月份
    print_colored("\n📅 测试多个月份 (1-3月):", "blue")
    urls = tool.generate_monthly_urls(1, 3)
    for url, month in urls:
        print_colored(f"  {month}月: {url}", "white")
    
    # 测试全年
    print_colored("\n📅 测试全年 (1-12月):", "blue")
    urls = tool.generate_monthly_urls(1, 12)
    print_colored(f"  生成了 {len(urls)} 个URL", "green")
    for url, month in urls[:3]:  # 只显示前3个
        print_colored(f"  {month}月: {url}", "white")
    print_colored("  ...", "white")
    for url, month in urls[-2:]:  # 显示最后2个
        print_colored(f"  {month}月: {url}", "white")


def test_date_range_calculation():
    """测试日期范围计算"""
    print_colored("\n🧪 测试日期范围计算", "cyan")
    print_colored("=" * 50, "cyan")
    
    browser_controller = None
    tool = SimplifiedExportTool(browser_controller)
    
    # 测试各个月份的日期范围
    months_info = [
        (1, "1月 (31天)"),
        (2, "2月 (28天, 2025年非闰年)"),
        (3, "3月 (31天)"),
        (4, "4月 (30天)"),
        (5, "5月 (31天)"),
        (6, "6月 (30天)"),
        (7, "7月 (31天)"),
        (8, "8月 (31天)"),
        (9, "9月 (30天)"),
        (10, "10月 (31天)"),
        (11, "11月 (30天)"),
        (12, "12月 (31天)")
    ]
    
    for month, description in months_info:
        start_date, end_date = tool.get_month_date_range(2025, month)
        print_colored(f"  {description}: {start_date} 至 {end_date}", "white")


def test_user_input_validation():
    """测试用户输入验证逻辑"""
    print_colored("\n🧪 测试输入验证逻辑", "cyan")
    print_colored("=" * 50, "cyan")
    
    # 这里只是展示验证逻辑，不实际获取用户输入
    test_cases = [
        (1, 1, "单月导出"),
        (1, 3, "季度导出"),
        (1, 12, "全年导出"),
        (6, 8, "夏季导出"),
        (10, 12, "年末导出")
    ]
    
    for start, end, description in test_cases:
        if 1 <= start <= 12 and 1 <= end <= 12 and end >= start:
            print_colored(f"  ✓ {description}: {start}月 - {end}月 (有效)", "green")
        else:
            print_colored(f"  ❌ {description}: {start}月 - {end}月 (无效)", "red")


async def test_browser_connection():
    """测试浏览器连接（需要Chrome运行）"""
    print_colored("\n🧪 测试浏览器连接", "cyan")
    print_colored("=" * 50, "cyan")
    
    try:
        browser_controller = BrowserController()
        success = await browser_controller.connect_to_existing_chrome()
        
        if success:
            print_colored("  ✓ 浏览器连接成功", "green")
            print_colored("  ✓ 可以进行实际的简化导出测试", "green")
            await browser_controller.close()
        else:
            print_colored("  ❌ 浏览器连接失败", "red")
            print_colored("  💡 请确保Chrome已启动并开启调试模式", "yellow")
            print_colored("  💡 启动命令: chrome.exe --remote-debugging-port=9222", "yellow")
            
    except Exception as e:
        print_colored(f"  ❌ 浏览器连接异常: {e}", "red")


def main():
    """主测试函数"""
    print_colored("🚀 简化导出工具测试", "green")
    print_colored("=" * 70, "green")
    
    # 运行各项测试
    test_url_generation()
    test_date_range_calculation()
    test_user_input_validation()
    
    # 异步测试浏览器连接
    print_colored("\n🔄 开始异步测试...", "blue")
    asyncio.run(test_browser_connection())
    
    print_colored("\n🎉 测试完成！", "green")
    print_colored("=" * 70, "green")
    print_colored("💡 如果浏览器连接成功，可以运行完整的简化导出工具", "cyan")
    print_colored("💡 运行命令: python main.py，然后选择选项4", "cyan")


if __name__ == "__main__":
    main()
