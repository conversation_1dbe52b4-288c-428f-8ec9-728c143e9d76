# 版本号: v1.0.0
# 变更记录:
# - v1.0.0: 初始版本，测试正常Chrome模式的简化导出工具

"""
正常Chrome模式测试脚本
测试在正常Chrome浏览器中的简化导出功能
"""

import asyncio
from simplified_export import SimplifiedExportTool
from utils import print_colored


async def test_normal_chrome_launch():
    """测试正常Chrome浏览器启动"""
    print_colored("🧪 测试正常Chrome浏览器启动", "cyan")
    print_colored("=" * 50, "cyan")
    
    tool = None
    try:
        # 创建简化导出工具
        tool = SimplifiedExportTool()
        
        # 测试浏览器初始化
        print_colored("🔄 正在初始化浏览器...", "blue")
        success = await tool.initialize_browser()
        
        if success:
            print_colored("✓ 浏览器初始化成功", "green")
            print_colored("✓ 可以看到Chrome浏览器窗口", "green")
            
            # 测试基本功能
            if tool.browser and tool.browser.page:
                page_title = await tool.browser.get_page_title()
                print_colored(f"✓ 当前页面标题: {page_title}", "white")
                
                page_url = tool.browser.page.url
                print_colored(f"✓ 当前页面URL: {page_url}", "white")
            
            print_colored("\n💡 浏览器将在5秒后自动关闭...", "yellow")
            await asyncio.sleep(5)
            
        else:
            print_colored("❌ 浏览器初始化失败", "red")
            
    except Exception as e:
        print_colored(f"❌ 测试异常: {e}", "red")
    finally:
        if tool:
            await tool.cleanup()


async def test_url_generation_normal():
    """测试URL生成功能（正常模式）"""
    print_colored("\n🧪 测试URL生成功能", "cyan")
    print_colored("=" * 50, "cyan")
    
    # 创建工具实例（不初始化浏览器）
    tool = SimplifiedExportTool()
    
    # 测试URL生成
    print_colored("📅 测试1-3月URL生成:", "blue")
    urls = tool.generate_monthly_urls(1, 3)
    for url, month in urls:
        print_colored(f"  {month}月: {url[:80]}...", "white")
    
    print_colored(f"\n✓ 成功生成 {len(urls)} 个URL", "green")


def test_user_interaction_simulation():
    """模拟用户交互测试"""
    print_colored("\n🧪 模拟用户交互测试", "cyan")
    print_colored("=" * 50, "cyan")
    
    print_colored("📝 模拟用户输入场景:", "blue")
    
    scenarios = [
        ("1", "3", "✓ 有效输入: 1-3月"),
        ("6", "12", "✓ 有效输入: 6-12月"),
        ("1", "12", "✓ 有效输入: 全年"),
        ("5", "4", "❌ 无效输入: 结束月份小于开始月份"),
        ("0", "5", "❌ 无效输入: 开始月份超出范围"),
        ("3", "13", "❌ 无效输入: 结束月份超出范围")
    ]
    
    for start, end, description in scenarios:
        try:
            start_month = int(start)
            end_month = int(end)
            
            if (1 <= start_month <= 12 and 
                1 <= end_month <= 12 and 
                end_month >= start_month):
                print_colored(f"  {description}", "green")
            else:
                print_colored(f"  {description}", "red")
        except ValueError:
            print_colored(f"  ❌ 无效输入: 非数字", "red")


async def test_browser_features():
    """测试浏览器特性"""
    print_colored("\n🧪 测试浏览器特性", "cyan")
    print_colored("=" * 50, "cyan")
    
    tool = None
    try:
        tool = SimplifiedExportTool()
        
        print_colored("🔄 启动浏览器...", "blue")
        if await tool.initialize_browser():
            print_colored("✓ 浏览器启动成功", "green")
            
            # 测试导航功能
            print_colored("🔄 测试导航功能...", "blue")
            await tool.browser.page.goto("https://www.baidu.com", wait_until="networkidle")
            
            title = await tool.browser.get_page_title()
            print_colored(f"✓ 导航成功，页面标题: {title}", "green")
            
            # 等待用户观察
            print_colored("\n👀 请观察浏览器窗口，5秒后自动关闭...", "yellow")
            await asyncio.sleep(5)
            
        else:
            print_colored("❌ 浏览器启动失败", "red")
            
    except Exception as e:
        print_colored(f"❌ 测试异常: {e}", "red")
    finally:
        if tool:
            await tool.cleanup()


async def main():
    """主测试函数"""
    print_colored("🚀 正常Chrome模式测试", "green")
    print_colored("=" * 70, "green")
    print_colored("本测试将验证简化导出工具在正常Chrome模式下的功能", "white")
    print_colored("无需手动启动调试模式Chrome", "white")
    print_colored("=" * 70, "green")
    
    # 运行各项测试
    await test_url_generation_normal()
    test_user_interaction_simulation()
    
    print_colored("\n🔄 开始浏览器相关测试...", "blue")
    print_colored("注意: 接下来会自动启动Chrome浏览器窗口", "yellow")
    
    await test_normal_chrome_launch()
    await test_browser_features()
    
    print_colored("\n🎉 所有测试完成！", "green")
    print_colored("=" * 70, "green")
    print_colored("💡 如果所有测试通过，可以运行完整的简化导出工具", "cyan")
    print_colored("💡 运行命令: python main.py，然后选择选项4", "cyan")
    print_colored("💡 新版本无需手动启动调试模式Chrome", "cyan")


if __name__ == "__main__":
    asyncio.run(main())
