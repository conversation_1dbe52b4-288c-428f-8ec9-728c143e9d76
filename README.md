# 数据处理工具集

## 版本信息
- 版本号: v2.0.0
- 变更记录:
  - v1.0.0: 初始版本，实现绕过网站导出限制的自动化数据导出功能
  - v1.1.0: 添加自动登录功能，支持cookie自动登录，提升用户体验
  - v2.0.0: 整合多功能模块，创建统一主程序入口，提升用户体验

## 功能简介

这是一个集成多种数据处理功能的工具集，包含自动化数据导出工具、月份URL生成器和Cookie管理工具等模块。通过统一的主程序入口，用户可以方便地选择和使用不同的功能模块。

## 🚀 核心功能模块

### 1. 📊 自动化数据导出工具
专门用于绕过公司网站导出限制的自动化工具。网站限制每次只能导出3个月以内的数据，本工具通过自动化操作，按1个月为单位逐步导出全年（1月到12月）的数据。

### 2. 🔗 月份URL生成器
根据用户输入的月份数字生成特定的URL，支持单个月份或多个连续月份输入，自动计算日期范围并生成完整的URL链接。

### 3. ⚙️ Cookie管理工具
提供完整的Cookie管理功能，包括提取、测试、查看和删除Cookie，支持自动登录配置。

## 主要特性

- ✅ **统一入口**: 集成多个功能模块，通过清晰的菜单系统访问
- ✅ **智能时间分割**: 自动将全年数据分割为1个月的时间段（远低于3个月限制，更安全）
- ✅ **浏览器自动化**: 连接已登录的Chrome浏览器，无需重复登录
- ✅ **自动登录**: 支持cookie自动登录，一次配置，长期使用
- ✅ **智能元素识别**: 自动识别页面上的日期选择器和导出按钮
- ✅ **状态监控**: 实时监控导出进度和完成状态
- ✅ **错误重试**: 自动重试失败的导出操作
- ✅ **详细日志**: 完整的操作日志和进度报告
- ✅ **Cookie管理**: 提供完整的cookie管理工具
- ✅ **URL生成**: 快速生成带日期参数的URL链接
- ✅ **用户友好**: 清晰的界面设计和操作指引

## 系统要求

- Python 3.7+
- Google Chrome浏览器
- Windows/macOS/Linux

## 安装步骤

1. **安装Python依赖**:
   ```bash
   pip install -r requirements.txt
   ```

2. **安装Playwright浏览器**:
   ```bash
   playwright install chromium
   ```

## 🚀 快速开始

### 第一步：运行主程序
```bash
python main.py
```

程序会显示统一的主菜单，您可以选择需要使用的功能模块：

```
======================================================================
🚀 数据处理工具集 v2.0.0
======================================================================
请选择要使用的功能:

1. 📊 自动化数据导出工具
   - 绕过网站导出限制，自动导出全年数据
   - 支持自动登录和Cookie管理

2. 🔗 月份URL生成器
   - 根据月份输入生成特定URL
   - 支持单个或多个连续月份

3. ⚙️  Cookie管理工具
   - 提取、测试和管理登录Cookie
   - 支持自动登录配置

4. ❓ 帮助信息
   - 查看各功能模块的详细说明

0. 🚪 退出程序
======================================================================
```

### 第二步：选择功能模块

#### 使用自动化数据导出工具（选项1）

**首次使用建议先配置自动登录：**

1. **启动Chrome浏览器**：
   ```bash
   # Windows
   start_chrome_safe.bat
   ```

   或手动启动：
   ```bash
   # Windows
   chrome.exe --remote-debugging-port=9222 --user-data-dir="C:\temp\chrome_debug"

   # macOS
   /Applications/Google\ Chrome.app/Contents/MacOS/Google\ Chrome --remote-debugging-port=9222 --user-data-dir="/tmp/chrome_debug"

   # Linux
   google-chrome --remote-debugging-port=9222 --user-data-dir="/tmp/chrome_debug"
   ```

2. **配置自动登录**：
   - 在主菜单选择 `3. Cookie管理工具`
   - 选择 `1. 提取Cookie`
   - 按提示在浏览器中登录网站
   - 工具会自动提取并保存登录状态

3. **运行自动化导出**：
   - 返回主菜单选择 `1. 自动化数据导出工具`
   - 程序会自动使用保存的Cookie进行登录
   - 按提示确认导出设置并开始自动化流程

#### 使用月份URL生成器（选项2）

直接选择即可使用，支持：
- 输入单个月份：`3`
- 输入多个连续月份：`3,4,5` 或 `3 4 5`
- 自动生成完整的URL链接

#### 使用Cookie管理工具（选项3）

提供完整的Cookie管理功能：
- 提取Cookie（从已登录浏览器）
- 测试自动登录
- 查看Cookie信息
- 删除保存的Cookie

### 第三步：配置调整（可选）

如需调整配置，编辑 `config.py` 文件：

```python
# 目标网站URL
TARGET_URL = "https://tastien.tastientech.com/portal/expand-store/developStores/bunk?current=1&pageSize=10"

# 要导出的年份
EXPORT_YEAR = 2025

# 每次导出的月份数（建议保持为1，更安全）
MONTHS_PER_EXPORT = 1
```

## Cookie管理工具

### 提取Cookies
```bash
python extract_cookies.py
```
从已登录的浏览器中提取并保存cookies。

### 测试自动登录
```bash
python test_auto_login.py
```
测试保存的cookies是否能够成功自动登录。

### Cookie管理命令行工具
```bash
# 查看cookie信息
python cookie_cli.py info

# 查看cookie详细信息
python cookie_cli.py details

# 删除保存的cookies
python cookie_cli.py delete

# 显示帮助
python cookie_cli.py help
```

## 工作流程

1. **连接浏览器**: 工具会自动连接到已启动的Chrome浏览器
2. **导航页面**: 自动导航到目标数据页面
3. **生成时间范围**: 按1个月为单位生成全年的时间范围列表
4. **逐个导出**: 
   - 设置时间范围
   - 触发导出操作
   - 等待导出完成
   - 处理下一个时间段
5. **生成报告**: 显示导出成功和失败的详细报告

## 时间范围示例

对于2025年，工具会生成以下时间范围：
- 2025-01-01 至 2025-01-31 (1月)
- 2025-02-01 至 2025-02-28 (2月)
- 2025-03-01 至 2025-03-31 (3月)
- 2025-04-01 至 2025-04-30 (4月)
- 2025-05-01 至 2025-05-31 (5月)
- 2025-06-01 至 2025-06-30 (6月)
- 2025-07-01 至 2025-07-31 (7月)
- 2025-08-01 至 2025-08-31 (8月)
- 2025-09-01 至 2025-09-30 (9月)
- 2025-10-01 至 2025-10-31 (10月)
- 2025-11-01 至 2025-11-30 (11月)
- 2025-12-01 至 2025-12-31 (12月)

## 故障排除

### 常见问题

1. **连接Chrome失败**
   - 确保Chrome已启动并开启远程调试模式
   - 检查端口9222是否被占用
   - 尝试重启Chrome浏览器

2. **找不到页面元素**
   - 网站页面结构可能已更改
   - 需要更新 `export_handler.py` 中的元素选择器

3. **导出超时**
   - 增加 `config.py` 中的 `EXPORT_WAIT_TIMEOUT` 值
   - 检查网络连接是否稳定

4. **权限问题**
   - 确保已在浏览器中完成登录
   - 检查账户是否有导出权限

### 调试模式

如需查看详细的调试信息，可以修改 `config.py` 中的日志级别：

```python
LOG_LEVEL = "DEBUG"
```

## 注意事项

- ⚠️ 请确保在使用前已在浏览器中完成登录
- ⚠️ 导出过程中请勿关闭Chrome浏览器
- ⚠️ 建议在网络稳定的环境下使用
- ⚠️ 如果网站页面结构发生变化，可能需要更新元素选择器

## 🏗️ 技术架构

```
main.py                      # 统一主程序入口和菜单系统
├── 自动化数据导出模块
│   ├── time_generator.py    # 时间范围生成器
│   ├── browser_controller.py # 浏览器控制器（支持自动登录）
│   └── export_handler.py    # 导出处理器
├── 月份URL生成器模块
│   └── month_url_generator.py # 月份URL生成器
├── Cookie管理模块
│   ├── cookie_manager.py    # Cookie管理器
│   ├── extract_cookies.py   # Cookie提取工具
│   ├── test_auto_login.py   # 自动登录测试工具
│   └── cookie_cli.py        # Cookie管理命令行工具
├── 通用模块
│   ├── utils.py            # 通用工具函数
│   └── config.py           # 配置文件
└── 启动脚本
    └── start_chrome_safe.bat # Chrome启动脚本
```

### 🔧 核心特性

- **统一入口**: 通过主菜单系统整合所有功能模块
- **模块化设计**: 各功能模块保持独立，松耦合高内聚
- **用户友好**: 清晰的界面设计和操作指引
- **错误处理**: 完善的异常处理和用户提示
- **扩展性**: 易于添加新的功能模块

## 许可证

本工具仅供学习和内部使用，请遵守相关法律法规和公司政策。
