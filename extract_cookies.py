# 版本号: v1.0.0
# 变更记录: 
# - v1.0.0: 初始版本，实现从已登录浏览器提取cookies功能

"""
Cookie提取脚本
用于从已登录的Chrome浏览器中提取cookies并保存
"""

import asyncio
import sys
from urllib.parse import urlparse

from browser_controller import BrowserController
from utils import print_colored
import config

async def main():
    """主函数"""
    print_colored("=== Cookie提取工具 ===", "cyan")
    print_colored("此工具将从已登录的Chrome浏览器中提取cookies", "blue")
    print()
    
    controller = BrowserController()
    
    try:
        # 连接到现有的Chrome浏览器
        print_colored("步骤1: 连接到Chrome浏览器...", "blue")
        connected = await controller.connect_to_existing_chrome()
        
        if not connected:
            print_colored("✗ 无法连接到Chrome浏览器", "red")
            print_colored("请确保:", "yellow")
            print_colored("1. Chrome浏览器已通过start_chrome_safe.bat启动", "yellow")
            print_colored("2. 浏览器正在运行且可访问", "yellow")
            return False
        
        # 检查当前cookie状态
        print_colored("\n步骤2: 检查现有cookie状态...", "blue")
        cookie_info = controller.get_cookie_status()
        
        if cookie_info.get("exists"):
            print_colored(f"发现现有cookies:", "yellow")
            print_colored(f"  - Cookie数量: {cookie_info.get('cookie_count', 0)}", "yellow")
            print_colored(f"  - 保存时间: {cookie_info.get('saved_at', 'N/A')}", "yellow")
            print_colored(f"  - 过期时间: {cookie_info.get('expires_at', 'N/A')}", "yellow")
            print_colored(f"  - 是否过期: {'是' if cookie_info.get('is_expired') else '否'}", "yellow")
            
            if not cookie_info.get('is_expired'):
                print_colored("\n现有cookies仍然有效，是否要重新提取？(y/N): ", "yellow", end="")
                choice = input().strip().lower()
                if choice not in ['y', 'yes']:
                    print_colored("取消操作", "yellow")
                    return True
        
        # 导航到目标页面
        print_colored("\n步骤3: 导航到目标页面...", "blue")
        success = await controller.navigate_to_target_page(config.TARGET_URL)
        
        if not success:
            print_colored("✗ 无法导航到目标页面", "red")
            return False
        
        # 检查登录状态
        print_colored("\n步骤4: 检查登录状态...", "blue")
        current_url = controller.page.url
        print_colored(f"当前页面: {current_url}", "blue")

        # 检查是否在登录页面
        if any(keyword in current_url.lower() for keyword in ['login', 'signin', 'auth']):
            print_colored("✗ 检测到当前在登录页面，请先手动登录", "red")
            print_colored("请在浏览器中完成登录，然后重新运行此脚本", "yellow")
            print_colored("\n登录步骤:", "yellow")
            print_colored("1. 在浏览器中输入用户名和密码", "white")
            print_colored("2. 点击登录按钮", "white")
            print_colored("3. 等待页面跳转到主页面", "white")
            print_colored("4. 重新运行此脚本", "white")
            return False

        # 等待页面完全加载
        print_colored("等待页面完全加载...", "blue")
        await asyncio.sleep(2)
        
        # 提取域名
        parsed_url = urlparse(config.TARGET_URL)
        domain = parsed_url.netloc
        print_colored(f"目标域名: {domain}", "blue")

        # 提取并保存cookies
        print_colored("\n步骤5: 提取并保存cookies...", "blue")
        print_colored("正在提取所有相关的cookies...", "blue")

        # 先尝试提取指定域名的cookies
        success = await controller.extract_and_save_cookies(domain)

        if not success:
            print_colored("指定域名cookie提取失败，尝试提取所有cookies...", "yellow")
            success = await controller.extract_and_save_cookies(None)  # 提取所有cookies
        
        if success:
            print_colored("\n✓ Cookie提取完成！", "green")
            print_colored("现在可以使用自动登录功能了", "green")
            
            # 显示提取的cookie信息
            cookie_info = controller.get_cookie_status()
            print_colored(f"\nCookie信息:", "cyan")
            print_colored(f"  - Cookie数量: {cookie_info.get('cookie_count', 0)}", "cyan")
            print_colored(f"  - 保存时间: {cookie_info.get('saved_at', 'N/A')}", "cyan")
            print_colored(f"  - 过期时间: {cookie_info.get('expires_at', 'N/A')}", "cyan")
            
            return True
        else:
            print_colored("✗ Cookie提取失败", "red")
            return False
            
    except KeyboardInterrupt:
        print_colored("\n用户取消操作", "yellow")
        return False
        
    except Exception as e:
        print_colored(f"\n✗ 发生错误: {e}", "red")
        return False
        
    finally:
        # 关闭浏览器连接
        await controller.close()

def show_usage():
    """显示使用说明"""
    print_colored("=== Cookie提取工具使用说明 ===", "cyan")
    print()
    print_colored("使用步骤:", "blue")
    print_colored("1. 运行 start_chrome_safe.bat 启动Chrome浏览器", "white")
    print_colored("2. 在浏览器中手动登录到目标网站", "white")
    print_colored("3. 运行此脚本提取cookies: python extract_cookies.py", "white")
    print_colored("4. 之后可以使用自动登录功能", "white")
    print()
    print_colored("注意事项:", "yellow")
    print_colored("- 确保Chrome浏览器通过调试模式启动", "white")
    print_colored("- 确保已经成功登录到目标网站", "white")
    print_colored("- 提取的cookies有效期为30天", "white")
    print()

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] in ['-h', '--help', 'help']:
        show_usage()
    else:
        try:
            result = asyncio.run(main())
            if result:
                print_colored("\n操作成功完成！", "green")
                sys.exit(0)
            else:
                print_colored("\n操作失败！", "red")
                sys.exit(1)
        except Exception as e:
            print_colored(f"\n程序异常退出: {e}", "red")
            sys.exit(1)
