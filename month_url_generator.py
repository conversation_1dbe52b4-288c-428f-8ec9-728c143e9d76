#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
月份URL生成器
版本: v1.1.0
创建日期: 2025-07-31
变更记录:
- v1.0.0 (2025-07-31): 初始版本，实现基本的月份输入和URL生成功能
- v1.1.0 (2025-07-31): 模块化重构，支持作为独立模块调用
"""

import re
from typing import List, Tuple


# 常量定义
BASE_URL = "https://tastien.tastientech.com/portal/expand-store/developStores/bunk?current=1&pageSize=10&submitTime%5B0%5D={start_date}&submitTime%5B1%5D={end_date}"
YEAR = 2025
MAX_MONTHS = 3
MONTH_NAMES = {
    1: "1月", 2: "2月", 3: "3月", 4: "4月", 5: "5月", 6: "6月",
    7: "7月", 8: "8月", 9: "9月", 10: "10月", 11: "11月", 12: "12月"
}


def is_leap_year(year: int) -> bool:
    """
    判断是否为闰年
    
    Args:
        year: 年份
        
    Returns:
        bool: 是否为闰年
    """
    return (year % 4 == 0 and year % 100 != 0) or (year % 400 == 0)


def get_month_days(year: int, month: int) -> int:
    """
    获取指定年月的天数
    
    Args:
        year: 年份
        month: 月份 (1-12)
        
    Returns:
        int: 该月的天数
    """
    if month in [1, 3, 5, 7, 8, 10, 12]:
        return 31
    elif month in [4, 6, 9, 11]:
        return 30
    elif month == 2:
        return 29 if is_leap_year(year) else 28
    else:
        raise ValueError(f"无效的月份: {month}")


def parse_month_input(user_input: str) -> List[int]:
    """
    解析用户输入的月份字符串
    
    Args:
        user_input: 用户输入的字符串
        
    Returns:
        List[int]: 解析后的月份列表
        
    Raises:
        ValueError: 输入格式无效时抛出异常
    """
    if not user_input.strip():
        raise ValueError("输入不能为空")
    
    # 使用正则表达式分割输入，支持逗号、空格等分隔符
    parts = re.split(r'[,\s]+', user_input.strip())
    
    try:
        months = [int(part) for part in parts if part]
    except ValueError:
        raise ValueError("输入必须是数字")
    
    # 去重并排序
    months = sorted(list(set(months)))
    
    return months


def validate_months(months: List[int]) -> None:
    """
    验证月份列表的有效性
    
    Args:
        months: 月份列表
        
    Raises:
        ValueError: 验证失败时抛出异常
    """
    if not months:
        raise ValueError("请至少输入一个月份")
    
    # 检查月份范围
    for month in months:
        if month < 1 or month > 12:
            raise ValueError(f"月份必须在1-12之间，无效月份: {month}")
    
    # 检查数量限制
    if len(months) > MAX_MONTHS:
        raise ValueError(f"最多只能输入{MAX_MONTHS}个月份，您输入了{len(months)}个")
    
    # 检查连续性（如果有多个月份）
    if len(months) > 1:
        for i in range(1, len(months)):
            if months[i] - months[i-1] != 1:
                month_names = [MONTH_NAMES[m] for m in months]
                raise ValueError(f"月份必须是连续的，您输入的月份: {', '.join(month_names)}")


def generate_url(months: List[int], year: int = YEAR) -> str:
    """
    根据月份列表生成URL
    
    Args:
        months: 月份列表
        year: 年份，默认为2025
        
    Returns:
        str: 生成的URL
    """
    min_month = min(months)
    max_month = max(months)
    
    # 计算开始日期（最小月份的第一天）
    start_date = f"{year}-{min_month:02d}-01"
    
    # 计算结束日期（最大月份的最后一天）
    end_day = get_month_days(year, max_month)
    end_date = f"{year}-{max_month:02d}-{end_day:02d}"
    
    # 生成URL
    url = BASE_URL.format(start_date=start_date, end_date=end_date)
    
    return url


def display_month_info(months: List[int]) -> None:
    """
    显示月份信息
    
    Args:
        months: 月份列表
    """
    month_names = [MONTH_NAMES[m] for m in months]
    if len(months) == 1:
        print(f"选择的月份: {month_names[0]}")
    else:
        print(f"选择的月份: {', '.join(month_names)} (共{len(months)}个月)")


class MonthUrlGenerator:
    """月份URL生成器类"""

    def __init__(self, year: int = YEAR, base_url: str = BASE_URL):
        """
        初始化月份URL生成器

        Args:
            year: 年份，默认为2025
            base_url: 基础URL模板
        """
        self.year = year
        self.base_url = base_url

    def process_month_input(self, user_input: str) -> str:
        """
        处理用户输入并生成URL

        Args:
            user_input: 用户输入的月份字符串

        Returns:
            str: 生成的URL

        Raises:
            ValueError: 输入无效时抛出异常
        """
        # 解析输入
        months = parse_month_input(user_input)

        # 验证输入
        validate_months(months)

        # 生成URL
        url = generate_url(months, self.year)

        return url

    def get_month_info(self, user_input: str) -> dict:
        """
        获取月份信息

        Args:
            user_input: 用户输入的月份字符串

        Returns:
            dict: 包含月份信息的字典
        """
        months = parse_month_input(user_input)
        validate_months(months)

        month_names = [MONTH_NAMES[m] for m in months]
        start_date = f"{self.year}-{min(months):02d}-01"
        end_day = get_month_days(self.year, max(months))
        end_date = f"{self.year}-{max(months):02d}-{end_day:02d}"

        return {
            'months': months,
            'month_names': month_names,
            'start_date': start_date,
            'end_date': end_date,
            'url': generate_url(months, self.year)
        }


def run_interactive_mode():
    """
    运行交互模式
    """
    print("=" * 60)
    print("月份URL生成器 v1.1.0")
    print("=" * 60)
    print("功能说明:")
    print("- 输入1-12之间的数字代表月份")
    print("- 支持单个月份或多个连续月份（最多3个）")
    print("- 多个月份可用逗号或空格分隔，如: 3,4,5 或 3 4 5")
    print("- 输入 'q' 或 'quit' 退出程序")
    print("=" * 60)

    generator = MonthUrlGenerator()

    while True:
        try:
            user_input = input("\n请输入月份 (1-12): ").strip()

            # 检查退出命令
            if user_input.lower() in ['q', 'quit', '退出']:
                print("感谢使用，再见！")
                break

            # 获取月份信息
            info = generator.get_month_info(user_input)

            # 显示月份信息
            if len(info['months']) == 1:
                print(f"选择的月份: {info['month_names'][0]}")
            else:
                print(f"选择的月份: {', '.join(info['month_names'])} (共{len(info['months'])}个月)")

            # 显示结果
            print(f"\n生成的URL:")
            print(f"{info['url']}")
            print(f"\n日期范围: {info['start_date']} 至 {info['end_date']}")

        except ValueError as e:
            print(f"❌ 输入错误: {e}")
        except KeyboardInterrupt:
            print("\n\n程序被用户中断，再见！")
            break
        except Exception as e:
            print(f"❌ 发生未知错误: {e}")


def main():
    """
    主程序入口
    """
    run_interactive_mode()


if __name__ == "__main__":
    main()
