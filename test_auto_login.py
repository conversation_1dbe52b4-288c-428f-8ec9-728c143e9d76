# 版本号: v1.0.0
# 变更记录: 
# - v1.0.0: 初始版本，实现自动登录测试功能

"""
自动登录测试脚本
用于测试cookie自动登录功能是否正常工作
"""

import asyncio
import sys

from browser_controller import BrowserController
from utils import print_colored
import config

async def test_auto_login():
    """测试自动登录功能"""
    print_colored("=== 自动登录测试工具 ===", "cyan")
    print_colored("此工具将测试cookie自动登录功能", "blue")
    print()
    
    controller = BrowserController()
    
    try:
        # 检查cookie状态
        print_colored("步骤1: 检查cookie状态...", "blue")
        cookie_info = controller.get_cookie_status()
        
        if not cookie_info.get("exists"):
            print_colored("✗ 没有找到保存的cookies", "red")
            print_colored("请先运行 extract_cookies.py 提取cookies", "yellow")
            return False
        
        if cookie_info.get("is_expired"):
            print_colored("✗ 保存的cookies已过期", "red")
            print_colored("请重新运行 extract_cookies.py 提取新的cookies", "yellow")
            return False
        
        print_colored("✓ 找到有效的cookies", "green")
        print_colored(f"  - Cookie数量: {cookie_info.get('cookie_count', 0)}", "cyan")
        print_colored(f"  - 保存时间: {cookie_info.get('saved_at', 'N/A')}", "cyan")
        print_colored(f"  - 剩余天数: {cookie_info.get('days_until_expiry', 0)}", "cyan")
        
        # 连接到Chrome浏览器
        print_colored("\n步骤2: 连接到Chrome浏览器...", "blue")
        connected = await controller.connect_to_existing_chrome()
        
        if not connected:
            print_colored("✗ 无法连接到Chrome浏览器", "red")
            print_colored("请确保:", "yellow")
            print_colored("1. Chrome浏览器已通过start_chrome_safe.bat启动", "yellow")
            print_colored("2. 浏览器正在运行且可访问", "yellow")
            return False
        
        # 测试自动登录
        print_colored("\n步骤3: 测试自动登录...", "blue")
        login_success = await controller.auto_login_with_cookies()
        
        if login_success:
            print_colored("✓ 自动登录测试成功！", "green")
            
            # 获取当前页面信息
            current_url = controller.page.url
            page_title = await controller.get_page_title()
            
            print_colored(f"\n当前页面信息:", "cyan")
            print_colored(f"  - URL: {current_url}", "cyan")
            print_colored(f"  - 标题: {page_title}", "cyan")
            
            return True
        else:
            print_colored("✗ 自动登录测试失败", "red")
            print_colored("可能的原因:", "yellow")
            print_colored("1. Cookies已失效", "yellow")
            print_colored("2. 网站登录机制发生变化", "yellow")
            print_colored("3. 网络连接问题", "yellow")
            
            return False
            
    except KeyboardInterrupt:
        print_colored("\n用户取消操作", "yellow")
        return False
        
    except Exception as e:
        print_colored(f"\n✗ 测试过程中发生错误: {e}", "red")
        return False
        
    finally:
        # 关闭浏览器连接
        await controller.close()

async def main():
    """主函数"""
    try:
        result = await test_auto_login()
        
        if result:
            print_colored("\n🎉 自动登录功能正常工作！", "green")
            print_colored("现在可以使用主程序进行自动化导出了", "green")
            return 0
        else:
            print_colored("\n❌ 自动登录功能存在问题", "red")
            print_colored("请检查上述提示并重新配置", "red")
            return 1
            
    except Exception as e:
        print_colored(f"\n程序异常退出: {e}", "red")
        return 1

def show_usage():
    """显示使用说明"""
    print_colored("=== 自动登录测试工具使用说明 ===", "cyan")
    print()
    print_colored("使用步骤:", "blue")
    print_colored("1. 确保已经通过 extract_cookies.py 提取了cookies", "white")
    print_colored("2. 运行 start_chrome_safe.bat 启动Chrome浏览器", "white")
    print_colored("3. 运行此脚本测试自动登录: python test_auto_login.py", "white")
    print()
    print_colored("测试内容:", "yellow")
    print_colored("- 检查保存的cookies是否存在和有效", "white")
    print_colored("- 连接到Chrome浏览器", "white")
    print_colored("- 尝试使用cookies自动登录", "white")
    print_colored("- 验证登录状态", "white")
    print()

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] in ['-h', '--help', 'help']:
        show_usage()
    else:
        try:
            exit_code = asyncio.run(main())
            sys.exit(exit_code)
        except Exception as e:
            print_colored(f"\n程序异常退出: {e}", "red")
            sys.exit(1)
