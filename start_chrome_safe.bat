@echo off
REM Chrome Safe Debug Mode Startup Script v1.0.0
REM Removed unsafe startup parameters for better stability and security

echo ===================================
echo    Chrome Safe Debug Mode Startup
echo ===================================
echo.

REM Create temp directory
set CHROME_DEBUG_DIR=%TEMP%\chrome_debug_auto_export_safe
if not exist "%CHROME_DEBUG_DIR%" mkdir "%CHROME_DEBUG_DIR%"

echo Starting Chrome with safe remote debugging...
echo Debug port: 9222
echo User data dir: %CHROME_DEBUG_DIR%
echo Security: Enhanced (removed unsafe flags)
echo.

REM Close existing Chrome processes
echo Closing existing Chrome processes...
taskkill /f /im chrome.exe >nul 2>&1
timeout /t 2 >nul

REM Try multiple Chrome installation paths
set CHROME_FOUND=0

REM Path 1: Program Files
if exist "C:\Program Files\Google\Chrome\Application\chrome.exe" (
    echo Found Chrome: C:\Program Files\Google\Chrome\Application\chrome.exe
    start "" "C:\Program Files\Google\Chrome\Application\chrome.exe" --remote-debugging-port=9222 --user-data-dir="%CHROME_DEBUG_DIR%"
    set CHROME_FOUND=1
    goto :chrome_started
)

REM Path 2: Program Files (x86)
if exist "C:\Program Files (x86)\Google\Chrome\Application\chrome.exe" (
    echo Found Chrome: C:\Program Files (x86)\Google\Chrome\Application\chrome.exe
    start "" "C:\Program Files (x86)\Google\Chrome\Application\chrome.exe" --remote-debugging-port=9222 --user-data-dir="%CHROME_DEBUG_DIR%"
    set CHROME_FOUND=1
    goto :chrome_started
)

REM Path 3: User AppData
if exist "%LOCALAPPDATA%\Google\Chrome\Application\chrome.exe" (
    echo Found Chrome: %LOCALAPPDATA%\Google\Chrome\Application\chrome.exe
    start "" "%LOCALAPPDATA%\Google\Chrome\Application\chrome.exe" --remote-debugging-port=9222 --user-data-dir="%CHROME_DEBUG_DIR%"
    set CHROME_FOUND=1
    goto :chrome_started
)

REM Try from PATH
echo Trying to start Chrome from PATH...
chrome.exe --remote-debugging-port=9222 --user-data-dir="%CHROME_DEBUG_DIR%" >nul 2>&1
if %errorlevel% equ 0 (
    set CHROME_FOUND=1
    goto :chrome_started
)

REM If not found
if %CHROME_FOUND% equ 0 (
    echo.
    echo [ERROR] Chrome browser not found!
    echo.
    echo Please ensure Chrome is installed, or start Chrome manually:
    echo chrome.exe --remote-debugging-port=9222 --user-data-dir="%CHROME_DEBUG_DIR%"
    echo.
    pause
    exit /b 1
)

:chrome_started
echo.
echo [SUCCESS] Chrome started in safe debug mode
echo.
echo Important instructions:
echo 1. Please visit the following URL in the opened Chrome browser:
echo    https://tastien.tastientech.com/portal/expand-store/developStores/bunk?current=1^&pageSize=10
echo.
echo 2. Complete website login
echo.
echo 3. After login, run the main program:
echo    python main.py
echo.
echo 4. Or run directly:
echo    start.bat
echo.
echo Note: No security warnings should appear in the browser now!
echo.

REM Wait 3 seconds for Chrome to fully start
echo Waiting for Chrome to start...
timeout /t 3 >nul

REM Check if Chrome debug port is ready
echo Checking Chrome debug port...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:9222/json' -TimeoutSec 5; Write-Host '[SUCCESS] Chrome debug port is ready' -ForegroundColor Green; Write-Host 'No unsafe command line flags detected!' -ForegroundColor Green } catch { Write-Host '[WARNING] Chrome debug port not responding, please wait a moment' -ForegroundColor Yellow }"

echo.
echo Press any key to exit...
pause >nul
