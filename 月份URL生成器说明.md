# 月份URL生成器使用说明

**版本**: v1.0.0  
**创建日期**: 2025-07-31  
**变更记录**:
- v1.0.0 (2025-07-31): 初始版本，实现基本的月份输入和URL生成功能

## 🎯 功能概述

这是一个Python程序，允许用户通过输入月份数字来生成特定的URL，无需使用浏览器的日期输入框。

## ✨ 主要功能

### 1. 月份输入支持
- 支持输入1-12之间的数字，每个数字代表对应的月份
- 支持单个月份或多个连续月份的输入
- 支持多种输入格式：逗号分隔（`3,4,5`）或空格分隔（`3 4 5`）

### 2. 输入限制
- 月份数字必须在1-12范围内
- 最多只能输入连续的3个月份
- 多个月份必须是连续的（如3,4,5有效，但3,5,7无效）

### 3. URL生成规则
- **基础URL模板**: `https://tastien.tastientech.com/portal/expand-store/developStores/bunk?current=1&pageSize=10&submitTime%5B0%5D={开始日期}&submitTime%5B1%5D={结束日期}`
- **日期格式**: YYYY-MM-DD
- **年份**: 固定为2025年
- **单个月份**: 开始日期为该月第一天，结束日期为该月最后一天
- **多个月份**: 开始日期为最小月份的第一天，结束日期为最大月份的最后一天

## 🚀 快速开始

### 运行主程序
```bash
python month_url_generator.py
```

### 运行测试
```bash
python test_month_url_generator.py
```

## 📝 使用示例

### 单个月份
```
输入: 1
选择的月份: 1月
生成的URL: https://tastien.tastientech.com/portal/expand-store/developStores/bunk?current=1&pageSize=10&submitTime%5B0%5D=2025-01-01&submitTime%5B1%5D=2025-01-31
日期范围: 2025-01-01 至 2025-01-31
```

### 多个连续月份
```
输入: 3,4,5
选择的月份: 3月, 4月, 5月 (共3个月)
生成的URL: https://tastien.tastientech.com/portal/expand-store/developStores/bunk?current=1&pageSize=10&submitTime%5B0%5D=2025-03-01&submitTime%5B1%5D=2025-05-31
日期范围: 2025-03-01 至 2025-05-31
```

### 2月份（平年28天）
```
输入: 2
选择的月份: 2月
生成的URL: https://tastien.tastientech.com/portal/expand-store/developStores/bunk?current=1&pageSize=10&submitTime%5B0%5D=2025-02-01&submitTime%5B1%5D=2025-02-28
日期范围: 2025-02-01 至 2025-02-28
```

## ❌ 错误示例

### 无效输入示例
```
输入: 1,3,5
错误: ❌ 输入错误: 月份必须是连续的，您输入的月份: 1月, 3月, 5月

输入: 13
错误: ❌ 输入错误: 月份必须在1-12之间，无效月份: 13

输入: 1,2,3,4
错误: ❌ 输入错误: 最多只能输入3个月份，您输入了4个
```

## 🔧 程序特性

### ✅ 输入验证
- 月份范围验证（1-12）
- 连续性验证（多个月份必须连续）
- 数量限制验证（最多3个月份）
- 输入格式验证（必须是数字）

### ✅ 错误处理
- 友好的错误提示信息
- 输入格式错误处理
- 范围超出错误处理
- 连续性错误处理

### ✅ 用户体验
- 清晰的程序介绍和使用说明
- 支持多种输入格式（逗号、空格分隔）
- 支持退出命令（`q`、`quit`、`退出`）
- 显示选择的月份信息和日期范围

### ✅ 代码质量
- 模块化设计，函数职责单一
- 完整的单元测试覆盖
- 详细的文档字符串
- 类型提示支持
- 遵循PEP 8代码规范

## 📁 文件结构

```
├── month_url_generator.py      # 主程序文件
├── test_month_url_generator.py # 测试文件
└── 月份URL生成器说明.md        # 使用说明文档
```

## 🛠 技术实现

### 核心函数
- `parse_month_input()`: 解析用户输入
- `validate_months()`: 验证月份有效性
- `generate_url()`: 生成URL
- `get_month_days()`: 计算月份天数
- `is_leap_year()`: 判断闰年

### 特殊处理
- **闰年处理**: 自动识别闰年，正确计算2月天数
- **月份天数**: 准确计算每月天数（31天、30天、28/29天）
- **输入解析**: 支持多种分隔符，自动去重和排序
- **连续性检查**: 确保多个月份是连续的

## 🚪 退出程序
输入以下任一命令可退出程序：
- `q`
- `quit`
- `退出`
- `Ctrl+C`（键盘中断）

## 📋 系统要求
- Python 3.6+
- 标准库模块：`re`, `typing`, `unittest`

## 🧪 测试覆盖

程序包含完整的单元测试，覆盖以下功能：
- 闰年判断测试
- 月份天数计算测试
- 输入解析测试
- 月份验证测试
- URL生成测试

运行测试查看详细结果：
```bash
python test_month_url_generator.py
```

## 📄 许可证
此程序仅供学习和内部使用。
