# 版本号: v1.1.0
# 变更记录:
# - v1.0.0: 初始版本，实现完整的自动化导出流程
# - v1.1.0: 添加自动登录功能，支持cookie自动登录

"""
自动化数据导出工具主程序
绕过网站导出限制，自动导出全年数据
"""

import asyncio
import sys
from typing import List, Tuple

from time_generator import TimeRangeGenerator
from browser_controller import BrowserController
from export_handler import ExportHandler
from utils import setup_logger, print_colored, format_time_range
import config

logger = setup_logger(__name__)

class AutoExportTool:
    """自动化导出工具主类"""
    
    def __init__(self):
        """初始化自动化导出工具"""
        self.time_generator = TimeRangeGenerator(
            year=config.EXPORT_YEAR,
            months_per_range=config.MONTHS_PER_EXPORT
        )
        self.browser_controller = BrowserController()
        self.export_handler = None
        self.logger = logger
        
    async def initialize(self) -> bool:
        """
        初始化工具
        
        Returns:
            初始化是否成功
        """
        try:
            print_colored("=== 自动化数据导出工具 ===", "cyan")
            print_colored(f"目标年份: {config.EXPORT_YEAR}", "blue")
            print_colored(f"每次导出: {config.MONTHS_PER_EXPORT}个月", "blue")
            print_colored(f"目标网址: {config.TARGET_URL}", "blue")
            print_colored("=" * 50, "cyan")
            
            # 连接到Chrome浏览器
            if not await self.browser_controller.connect_to_existing_chrome():
                print_colored("请确保Chrome浏览器已启动并开启远程调试模式", "red")
                print_colored("启动命令: chrome.exe --remote-debugging-port=9222", "yellow")
                return False

            # 尝试自动登录
            print_colored("尝试自动登录...", "blue")
            auto_login_success = await self.browser_controller.auto_login_with_cookies()

            if auto_login_success:
                print_colored("✓ 自动登录成功", "green")
            else:
                print_colored("✗ 自动登录失败，需要手动登录", "yellow")
                print_colored("请在浏览器中完成登录，然后按回车继续...", "yellow")
                input()

                # 手动登录后，导航到目标页面
                if not await self.browser_controller.navigate_to_target_page(config.TARGET_URL):
                    return False

                # 询问是否保存cookies
                print_colored("是否保存当前登录状态的cookies？(Y/n): ", "yellow", end="")
                save_cookies = input().strip().lower()
                if save_cookies not in ['n', 'no', '否']:
                    cookie_saved = await self.browser_controller.extract_and_save_cookies()
                    if cookie_saved:
                        print_colored("✓ Cookies已保存，下次可以自动登录", "green")
                    else:
                        print_colored("✗ Cookies保存失败", "red")
            
            # 初始化导出处理器
            self.export_handler = ExportHandler(self.browser_controller)
            
            print_colored("✓ 工具初始化完成", "green")
            return True
            
        except Exception as e:
            print_colored(f"✗ 初始化失败: {e}", "red")
            self.logger.error(f"初始化失败: {e}")
            return False
    
    async def run_export_process(self) -> bool:
        """
        运行完整的导出流程
        
        Returns:
            导出流程是否成功完成
        """
        try:
            # 生成时间范围列表
            time_ranges = self.time_generator.generate_time_ranges()
            total_ranges = len(time_ranges)
            
            print_colored(f"\n生成了 {total_ranges} 个时间范围:", "blue")
            for i, (start, end) in enumerate(time_ranges, 1):
                description = self.time_generator.get_range_description(start, end)
                print_colored(f"  {i}. {description} ({start} 至 {end})", "white")
            
            # 确认是否继续
            print_colored(f"\n准备开始导出，预计需要 {total_ranges * 2} 分钟", "yellow")
            user_input = input("是否继续？(y/N): ").strip().lower()
            if user_input not in ['y', 'yes', '是']:
                print_colored("用户取消操作", "yellow")
                return False
            
            # 逐个处理时间范围
            successful_exports = 0
            failed_exports = []
            
            for i, (start_date, end_date) in enumerate(time_ranges, 1):
                print_colored(f"\n[{i}/{total_ranges}] 处理时间范围", "cyan")
                
                # 验证日期范围
                if not self.time_generator.validate_date_range(start_date, end_date):
                    print_colored(f"✗ 跳过无效的日期范围: {start_date} 至 {end_date}", "red")
                    failed_exports.append((start_date, end_date, "无效日期范围"))
                    continue
                
                # 执行导出
                retry_count = 0
                export_success = False
                
                while retry_count < config.MAX_RETRIES and not export_success:
                    if retry_count > 0:
                        print_colored(f"第 {retry_count + 1} 次重试...", "yellow")
                        await asyncio.sleep(config.RETRY_DELAY)
                    
                    export_success = await self.export_handler.export_data_for_range(
                        start_date, end_date
                    )
                    
                    if not export_success:
                        retry_count += 1
                
                if export_success:
                    successful_exports += 1
                    print_colored(f"✓ [{i}/{total_ranges}] 导出成功", "green")
                else:
                    failed_exports.append((start_date, end_date, "导出失败"))
                    print_colored(f"✗ [{i}/{total_ranges}] 导出失败", "red")
                
                # 进度报告
                print_colored(f"进度: {i}/{total_ranges} ({i/total_ranges*100:.1f}%)", "blue")
                
                # 如果不是最后一个，等待一段时间
                if i < total_ranges:
                    print_colored("等待下一个导出...", "yellow")
                    await asyncio.sleep(config.RETRY_DELAY)
            
            # 最终报告
            self._print_final_report(successful_exports, failed_exports, total_ranges)
            
            return len(failed_exports) == 0
            
        except Exception as e:
            print_colored(f"✗ 导出流程出错: {e}", "red")
            self.logger.error(f"导出流程出错: {e}")
            return False
    
    def _print_final_report(self, successful: int, failed: List[Tuple], total: int):
        """打印最终报告"""
        print_colored("\n" + "=" * 50, "cyan")
        print_colored("导出完成报告", "cyan")
        print_colored("=" * 50, "cyan")
        
        print_colored(f"总计时间范围: {total}", "blue")
        print_colored(f"成功导出: {successful}", "green")
        print_colored(f"失败导出: {len(failed)}", "red")
        print_colored(f"成功率: {successful/total*100:.1f}%", "blue")
        
        if failed:
            print_colored("\n失败的时间范围:", "red")
            for start, end, reason in failed:
                print_colored(f"  • {start} 至 {end} - {reason}", "red")
        
        print_colored("=" * 50, "cyan")
    
    async def cleanup(self):
        """清理资源"""
        try:
            if self.browser_controller:
                await self.browser_controller.close()
        except Exception as e:
            self.logger.error(f"清理资源时出错: {e}")

async def main():
    """主函数"""
    tool = AutoExportTool()
    
    try:
        # 初始化工具
        if not await tool.initialize():
            return 1
        
        # 运行导出流程
        success = await tool.run_export_process()
        
        if success:
            print_colored("\n🎉 所有数据导出完成！", "green")
            return 0
        else:
            print_colored("\n⚠️ 部分数据导出失败，请检查失败报告", "yellow")
            return 1
            
    except KeyboardInterrupt:
        print_colored("\n用户中断操作", "yellow")
        return 1
    except Exception as e:
        print_colored(f"\n程序异常退出: {e}", "red")
        logger.error(f"程序异常退出: {e}")
        return 1
    finally:
        await tool.cleanup()

if __name__ == "__main__":
    # 运行主程序
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
