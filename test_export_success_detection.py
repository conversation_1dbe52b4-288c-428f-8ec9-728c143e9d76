#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
导出成功检测测试脚本
版本: v1.0.0
创建日期: 2025-07-31
变更记录:
- v1.0.0 (2025-07-31): 初始版本，测试导出成功提示的检测功能
"""

import asyncio
import sys
from browser_controller import BrowserController
from export_handler import ExportHandler
from utils import setup_logger, print_colored

logger = setup_logger(__name__)

async def test_export_success_detection():
    """测试导出成功检测功能"""
    browser_controller = None
    
    try:
        print_colored("🧪 开始测试导出成功检测功能", "cyan")
        print_colored("=" * 60, "cyan")
        
        # 初始化浏览器控制器
        browser_controller = BrowserController()
        
        # 连接到Chrome浏览器
        print_colored("连接到Chrome浏览器...", "blue")
        if not await browser_controller.connect_to_existing_chrome():
            print_colored("❌ 无法连接到Chrome浏览器", "red")
            print_colored("请确保Chrome已启动并开启远程调试模式", "yellow")
            print_colored("启动命令: chrome.exe --remote-debugging-port=9222", "yellow")
            return False
        
        print_colored("✅ 成功连接到Chrome浏览器", "green")
        
        # 初始化导出处理器
        export_handler = ExportHandler(browser_controller)
        
        # 测试导出成功消息检测
        print_colored("\n🔍 测试导出成功消息检测...", "blue")
        
        # 检查当前页面是否有导出成功提示
        success_detected = await export_handler.check_export_success_message()
        
        if success_detected:
            print_colored("✅ 检测到导出成功提示！", "green")
        else:
            print_colored("ℹ️  当前页面未检测到导出成功提示", "yellow")
            print_colored("这是正常的，除非页面刚刚完成了导出操作", "white")
        
        # 显示当前页面的一些信息
        print_colored("\n📄 当前页面信息:", "blue")
        try:
            page_title = await browser_controller.page.title()
            page_url = browser_controller.page.url
            print_colored(f"页面标题: {page_title}", "white")
            print_colored(f"页面URL: {page_url}", "white")
        except Exception as e:
            print_colored(f"获取页面信息失败: {e}", "red")
        
        # 测试选择器配置
        print_colored("\n🔧 测试选择器配置:", "blue")
        selectors_to_test = [
            ("success_message", "成功消息选择器"),
            ("export_success_text", "导出成功文本选择器"),
            ("loading_indicator", "加载指示器选择器"),
            ("error_message", "错误消息选择器")
        ]
        
        for selector_key, description in selectors_to_test:
            try:
                selector = export_handler.selectors[selector_key]
                elements = await browser_controller.page.query_selector_all(selector)
                count = len(elements)
                if count > 0:
                    print_colored(f"  {description}: 找到 {count} 个元素", "green")
                else:
                    print_colored(f"  {description}: 未找到元素", "white")
            except Exception as e:
                print_colored(f"  {description}: 测试失败 - {e}", "red")
        
        print_colored("\n✅ 导出成功检测测试完成", "green")
        return True
        
    except Exception as e:
        print_colored(f"❌ 测试过程中出现错误: {e}", "red")
        logger.error(f"测试过程中出现错误: {e}")
        return False
        
    finally:
        if browser_controller:
            try:
                await browser_controller.close()
            except Exception as e:
                logger.error(f"关闭浏览器时出错: {e}")

async def main():
    """主函数"""
    print_colored("🚀 导出成功检测测试工具", "cyan")
    print_colored("此工具用于测试导出成功提示的检测功能", "white")
    print_colored("请确保Chrome浏览器已启动并开启远程调试模式", "yellow")
    print_colored("", "white")
    
    try:
        success = await test_export_success_detection()
        
        if success:
            print_colored("\n🎉 测试成功完成！", "green")
            return 0
        else:
            print_colored("\n❌ 测试失败", "red")
            return 1
            
    except KeyboardInterrupt:
        print_colored("\n⚠️ 测试被用户中断", "yellow")
        return 1
    except Exception as e:
        print_colored(f"\n❌ 测试异常退出: {e}", "red")
        logger.error(f"测试异常退出: {e}")
        return 1

if __name__ == "__main__":
    # 运行测试
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
