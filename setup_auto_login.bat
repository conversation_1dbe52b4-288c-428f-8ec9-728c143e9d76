@echo off
chcp 65001 >nul
echo ========================================
echo 自动登录设置向导
echo ========================================
echo.

echo 此脚本将帮助您设置自动登录功能
echo.

echo 步骤1: 启动Chrome浏览器...
echo 正在启动Chrome浏览器（调试模式）...
start "" "start_chrome_safe.bat"

echo.
echo 等待Chrome浏览器启动...
timeout /t 3 /nobreak >nul

echo.
echo 步骤2: 请在Chrome浏览器中完成以下操作：
echo 1. 访问目标网站
echo 2. 完成登录操作
echo 3. 确保登录成功
echo.
echo 完成登录后，按任意键继续...
pause >nul

echo.
echo 步骤3: 提取登录cookies...
python extract_cookies.py

if %errorlevel% neq 0 (
    echo.
    echo ❌ Cookie提取失败！
    echo 请检查：
    echo 1. Chrome浏览器是否正常启动
    echo 2. 是否已成功登录到目标网站
    echo 3. Python环境是否正确配置
    echo.
    pause
    exit /b 1
)

echo.
echo 步骤4: 测试自动登录...
python test_auto_login.py

if %errorlevel% neq 0 (
    echo.
    echo ❌ 自动登录测试失败！
    echo 请重新运行此脚本或手动检查配置
    echo.
    pause
    exit /b 1
)

echo.
echo ========================================
echo ✅ 自动登录设置完成！
echo ========================================
echo.
echo 现在您可以：
echo 1. 运行 python main.py 开始自动化导出
echo 2. 使用 python cookie_cli.py info 查看cookie状态
echo 3. 使用 python test_auto_login.py 测试自动登录
echo.
echo 注意：cookies有效期为30天，过期后需要重新设置
echo.
pause
