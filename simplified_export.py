# 版本号: v1.1.0
# 变更记录:
# - v1.0.0: 初始版本，实现简化的自动化导出功能，通过直接URL访问替代复杂页面操作
# - v1.1.0: 修改为使用正常Chrome浏览器模式，无需调试模式，提升用户体验

"""
简化导出工具模块
通过直接URL访问的方式实现自动化导出，提升效率和用户体验
"""

import asyncio
from typing import List, Tuple, Optional
from datetime import datetime
import urllib.parse

from browser_controller import BrowserController
from utils import setup_logger, print_colored
import config

logger = setup_logger(__name__)


class SimplifiedExportTool:
    """简化导出工具类"""

    def __init__(self, browser_controller: BrowserController = None):
        """
        初始化简化导出工具

        Args:
            browser_controller: 浏览器控制器实例，如果为None则自动创建
        """
        self.browser = browser_controller
        self.logger = logger
        self.base_url = "https://tastien.tastientech.com/portal/expand-store/developStores/bunk"
        self.base_params = "current=1&pageSize=10"
        self._browser_owned = browser_controller is None  # 标记是否由本类管理浏览器

        # 页面元素选择器
        self.selectors = {
            "search_button": "button:has-text('搜索'), button:has-text('查询'), .ant-btn:has-text('搜索')",
            "export_button": "button:has-text('导出业务'), button:has-text('导出'), .ant-btn:has-text('导出')",
            "loading_indicator": ".ant-spin-spinning, .loading, [class*='loading']"
        }

    async def initialize_browser(self) -> bool:
        """
        初始化浏览器（如果需要）

        Returns:
            初始化是否成功
        """
        try:
            if self.browser is None:
                print_colored("🚀 启动Chrome浏览器...", "blue")
                self.browser = BrowserController()

                # 启动正常的Chrome浏览器
                if not await self.browser.launch_normal_chrome():
                    print_colored("❌ Chrome浏览器启动失败", "red")
                    return False

                print_colored("✓ Chrome浏览器启动成功", "green")

                # 尝试加载保存的Cookie
                print_colored("🔄 尝试加载保存的登录状态...", "blue")
                cookie_loaded = await self.browser.load_and_apply_cookies()

                if cookie_loaded:
                    print_colored("✓ 登录状态加载成功", "green")
                    # 导航到目标页面验证登录状态
                    await self.browser.page.goto(config.TARGET_URL, wait_until="networkidle")
                    await asyncio.sleep(2)

                    # 简单检查是否需要登录（可以根据页面特征调整）
                    page_title = await self.browser.get_page_title()
                    if "登录" in page_title or "login" in page_title.lower():
                        print_colored("⚠️ 需要手动登录", "yellow")
                        return await self._handle_manual_login()
                    else:
                        print_colored("✓ 自动登录成功", "green")
                        return True
                else:
                    print_colored("⚠️ 没有保存的登录状态，需要手动登录", "yellow")
                    return await self._handle_manual_login()

            return True

        except Exception as e:
            print_colored(f"❌ 浏览器初始化失败: {e}", "red")
            self.logger.error(f"浏览器初始化失败: {e}")
            return False

    async def _handle_manual_login(self) -> bool:
        """
        处理手动登录流程

        Returns:
            登录是否成功
        """
        try:
            # 导航到目标页面
            print_colored("🔄 导航到登录页面...", "blue")
            await self.browser.page.goto(config.TARGET_URL, wait_until="networkidle")

            print_colored("\n" + "=" * 60, "yellow")
            print_colored("🔐 需要手动登录", "yellow")
            print_colored("=" * 60, "yellow")
            print_colored("请在打开的Chrome浏览器中完成登录操作", "white")
            print_colored("登录完成后，请按回车键继续...", "white")
            print_colored("=" * 60, "yellow")

            # 等待用户手动登录
            input()

            # 检查登录状态
            await asyncio.sleep(2)
            page_title = await self.browser.get_page_title()

            if "登录" not in page_title and "login" not in page_title.lower():
                print_colored("✓ 登录验证成功", "green")

                # 询问是否保存登录状态
                print_colored("💾 是否保存当前登录状态？(Y/n): ", "cyan", end="")
                save_cookies = input().strip().lower()

                if save_cookies not in ['n', 'no', '否']:
                    cookie_saved = await self.browser.extract_and_save_cookies()
                    if cookie_saved:
                        print_colored("✓ 登录状态已保存，下次将自动登录", "green")
                    else:
                        print_colored("⚠️ 登录状态保存失败", "yellow")

                return True
            else:
                print_colored("❌ 登录验证失败，请确保已正确登录", "red")
                return False

        except Exception as e:
            print_colored(f"❌ 手动登录处理失败: {e}", "red")
            self.logger.error(f"手动登录处理失败: {e}")
            return False
    
    def get_user_input(self) -> Tuple[Optional[int], Optional[int]]:
        """
        获取用户输入的月份范围
        
        Returns:
            (开始月份, 结束月份) 或 (None, None) 如果用户取消
        """
        print_colored("\n🚀 简化导出工具", "cyan")
        print_colored("=" * 50, "cyan")
        print_colored("请输入要导出的月份范围（2025年）:", "white")
        print_colored("", "white")
        
        try:
            # 获取开始月份
            while True:
                start_input = input("请输入开始月份 (1-12): ").strip()
                if not start_input:
                    print_colored("❌ 操作已取消", "yellow")
                    return None, None
                
                try:
                    start_month = int(start_input)
                    if 1 <= start_month <= 12:
                        break
                    else:
                        print_colored("❌ 请输入1-12之间的数字", "red")
                except ValueError:
                    print_colored("❌ 请输入有效的数字", "red")
            
            # 获取结束月份
            while True:
                end_input = input("请输入结束月份 (1-12): ").strip()
                if not end_input:
                    print_colored("❌ 操作已取消", "yellow")
                    return None, None
                
                try:
                    end_month = int(end_input)
                    if 1 <= end_month <= 12:
                        if end_month >= start_month:
                            break
                        else:
                            print_colored("❌ 结束月份必须大于等于开始月份", "red")
                    else:
                        print_colored("❌ 请输入1-12之间的数字", "red")
                except ValueError:
                    print_colored("❌ 请输入有效的数字", "red")
            
            return start_month, end_month
            
        except KeyboardInterrupt:
            print_colored("\n❌ 操作被用户中断", "yellow")
            return None, None
    
    def get_month_date_range(self, year: int, month: int) -> Tuple[str, str]:
        """
        获取指定月份的日期范围
        
        Args:
            year: 年份
            month: 月份 (1-12)
            
        Returns:
            (开始日期, 结束日期) 格式为 YYYY-MM-DD
        """
        # 每月天数（2025年不是闰年）
        days_in_month = {
            1: 31, 2: 28, 3: 31, 4: 30, 5: 31, 6: 30,
            7: 31, 8: 31, 9: 30, 10: 31, 11: 30, 12: 31
        }
        
        start_date = f"{year}-{month:02d}-01"
        end_date = f"{year}-{month:02d}-{days_in_month[month]:02d}"
        
        return start_date, end_date
    
    def generate_monthly_urls(self, start_month: int, end_month: int, year: int = 2025) -> List[Tuple[str, int]]:
        """
        生成每个月的URL列表
        
        Args:
            start_month: 开始月份
            end_month: 结束月份
            year: 年份，默认2025
            
        Returns:
            [(URL, 月份)] 列表
        """
        urls = []
        
        for month in range(start_month, end_month + 1):
            start_date, end_date = self.get_month_date_range(year, month)
            
            # 构建URL参数
            params = f"{self.base_params}&submitTime%5B0%5D={start_date}&submitTime%5B1%5D={end_date}"
            url = f"{self.base_url}?{params}"
            
            urls.append((url, month))
            
        return urls
    
    async def process_url(self, url: str, month: int, current: int, total: int) -> bool:
        """
        处理单个URL的访问和操作
        
        Args:
            url: 要访问的URL
            month: 当前月份
            current: 当前序号
            total: 总数量
            
        Returns:
            操作是否成功
        """
        try:
            print_colored(f"\n[{current}/{total}] 正在处理 {month}月 数据...", "blue")
            print_colored(f"访问URL: {url}", "white")
            
            # 访问URL
            await self.browser.page.goto(url, wait_until="networkidle")
            print_colored("✓ 页面加载完成", "green")
            
            # 等待页面稳定
            await asyncio.sleep(1)
            
            # 点击搜索按钮
            try:
                search_button = await self.browser.page.wait_for_selector(
                    self.selectors["search_button"], 
                    timeout=10000
                )
                await search_button.click()
                print_colored("✓ 已点击搜索按钮", "green")
            except Exception as e:
                print_colored(f"⚠️ 搜索按钮点击失败: {e}", "yellow")
                # 继续执行，可能页面已经自动搜索
            
            # 等待3秒
            print_colored("等待3秒...", "yellow")
            await asyncio.sleep(3)
            
            # 点击导出业务按钮
            try:
                export_button = await self.browser.page.wait_for_selector(
                    self.selectors["export_button"], 
                    timeout=10000
                )
                await export_button.click()
                print_colored("✓ 已点击导出业务按钮", "green")
            except Exception as e:
                print_colored(f"❌ 导出按钮点击失败: {e}", "red")
                return False
            
            # 等待3秒
            print_colored("等待3秒...", "yellow")
            await asyncio.sleep(3)
            
            print_colored(f"✓ [{current}/{total}] {month}月 数据处理完成", "green")
            return True
            
        except Exception as e:
            print_colored(f"❌ [{current}/{total}] {month}月 数据处理失败: {e}", "red")
            self.logger.error(f"处理URL失败 {url}: {e}")
            return False
    
    async def run_simplified_export(self) -> bool:
        """
        运行简化导出流程

        Returns:
            导出流程是否成功完成
        """
        try:
            # 初始化浏览器（如果需要）
            if not await self.initialize_browser():
                return False

            # 获取用户输入
            start_month, end_month = self.get_user_input()
            if start_month is None or end_month is None:
                return False
            
            # 生成URL列表
            urls = self.generate_monthly_urls(start_month, end_month)
            total_urls = len(urls)
            
            print_colored(f"\n📋 生成了 {total_urls} 个月份的URL:", "blue")
            for i, (url, month) in enumerate(urls, 1):
                print_colored(f"  {i}. {month}月: {url}", "white")
            
            # 确认是否继续
            print_colored(f"\n准备开始导出，预计需要 {total_urls * 0.5} 分钟", "yellow")
            user_input = input("是否继续？(y/N): ").strip().lower()
            if user_input not in ['y', 'yes', '是']:
                print_colored("❌ 用户取消操作", "yellow")
                return False
            
            # 逐个处理URL
            successful_count = 0
            failed_months = []
            
            for i, (url, month) in enumerate(urls, 1):
                success = await self.process_url(url, month, i, total_urls)
                
                if success:
                    successful_count += 1
                else:
                    failed_months.append(month)
                
                # 显示进度
                progress = i / total_urls * 100
                print_colored(f"总进度: {i}/{total_urls} ({progress:.1f}%)", "cyan")
                
                # 如果不是最后一个，稍作等待
                if i < total_urls:
                    await asyncio.sleep(1)
            
            # 打印最终报告
            self._print_final_report(successful_count, failed_months, total_urls, start_month, end_month)
            
            return len(failed_months) == 0
            
        except KeyboardInterrupt:
            print_colored("\n⚠️ 用户中断操作", "yellow")
            return False
        except Exception as e:
            print_colored(f"\n❌ 简化导出流程出错: {e}", "red")
            self.logger.error(f"简化导出流程出错: {e}")
            return False
    
    def _print_final_report(self, successful: int, failed_months: List[int], total: int, start_month: int, end_month: int):
        """打印最终报告"""
        print_colored("\n" + "=" * 60, "cyan")
        print_colored("🎉 简化导出完成报告", "cyan")
        print_colored("=" * 60, "cyan")
        
        print_colored(f"导出范围: {start_month}月 - {end_month}月 (2025年)", "blue")
        print_colored(f"总计月份: {total}", "blue")
        print_colored(f"成功处理: {successful}", "green")
        print_colored(f"失败处理: {len(failed_months)}", "red")
        print_colored(f"成功率: {successful/total*100:.1f}%", "blue")
        
        if failed_months:
            print_colored("\n❌ 失败的月份:", "red")
            for month in failed_months:
                print_colored(f"  • {month}月", "red")
            print_colored("\n💡 建议: 可以单独重新处理失败的月份", "yellow")
        else:
            print_colored("\n🎉 所有月份都处理成功！", "green")
        
        print_colored("=" * 60, "cyan")

    async def cleanup(self):
        """清理资源"""
        try:
            if self._browser_owned and self.browser:
                print_colored("🔄 正在关闭浏览器...", "blue")
                await self.browser.close()
                print_colored("✓ 浏览器已关闭", "green")
        except Exception as e:
            self.logger.error(f"清理资源时出错: {e}")
