#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
月份URL生成器测试文件
版本: v1.0.0
创建日期: 2025-07-31
变更记录:
- v1.0.0 (2025-07-31): 初始版本，包含基本功能测试用例
"""

import unittest
from month_url_generator import (
    parse_month_input, validate_months, generate_url, 
    get_month_days, is_leap_year
)


class TestMonthUrlGenerator(unittest.TestCase):
    """月份URL生成器测试类"""
    
    def test_is_leap_year(self):
        """测试闰年判断"""
        self.assertTrue(is_leap_year(2024))  # 闰年
        self.assertFalse(is_leap_year(2025))  # 平年
        self.assertTrue(is_leap_year(2000))   # 能被400整除
        self.assertFalse(is_leap_year(1900))  # 能被100整除但不能被400整除
    
    def test_get_month_days(self):
        """测试获取月份天数"""
        # 31天的月份
        self.assertEqual(get_month_days(2025, 1), 31)
        self.assertEqual(get_month_days(2025, 3), 31)
        self.assertEqual(get_month_days(2025, 12), 31)
        
        # 30天的月份
        self.assertEqual(get_month_days(2025, 4), 30)
        self.assertEqual(get_month_days(2025, 6), 30)
        
        # 2月
        self.assertEqual(get_month_days(2025, 2), 28)  # 平年
        self.assertEqual(get_month_days(2024, 2), 29)  # 闰年
    
    def test_parse_month_input(self):
        """测试输入解析"""
        # 单个月份
        self.assertEqual(parse_month_input("1"), [1])
        self.assertEqual(parse_month_input(" 5 "), [5])
        
        # 多个月份
        self.assertEqual(parse_month_input("1,2,3"), [1, 2, 3])
        self.assertEqual(parse_month_input("3 4 5"), [3, 4, 5])
        self.assertEqual(parse_month_input("5,4,3"), [3, 4, 5])  # 自动排序
        self.assertEqual(parse_month_input("1,1,2"), [1, 2])     # 自动去重
        
        # 异常情况
        with self.assertRaises(ValueError):
            parse_month_input("")
        with self.assertRaises(ValueError):
            parse_month_input("abc")
    
    def test_validate_months(self):
        """测试月份验证"""
        # 有效输入
        validate_months([1])
        validate_months([1, 2, 3])
        validate_months([10, 11, 12])
        
        # 无效输入
        with self.assertRaises(ValueError):
            validate_months([])  # 空列表
        with self.assertRaises(ValueError):
            validate_months([0])  # 超出范围
        with self.assertRaises(ValueError):
            validate_months([13])  # 超出范围
        with self.assertRaises(ValueError):
            validate_months([1, 2, 3, 4])  # 超过3个月
        with self.assertRaises(ValueError):
            validate_months([1, 3, 5])  # 不连续
    
    def test_generate_url(self):
        """测试URL生成"""
        # 单个月份
        url1 = generate_url([1])
        expected1 = "https://tastien.tastientech.com/portal/expand-store/developStores/bunk?current=1&pageSize=10&submitTime%5B0%5D=2025-01-01&submitTime%5B1%5D=2025-01-31"
        self.assertEqual(url1, expected1)
        
        # 多个月份
        url2 = generate_url([3, 4, 5])
        expected2 = "https://tastien.tastientech.com/portal/expand-store/developStores/bunk?current=1&pageSize=10&submitTime%5B0%5D=2025-03-01&submitTime%5B1%5D=2025-05-31"
        self.assertEqual(url2, expected2)
        
        # 2月测试
        url3 = generate_url([2])
        expected3 = "https://tastien.tastientech.com/portal/expand-store/developStores/bunk?current=1&pageSize=10&submitTime%5B0%5D=2025-02-01&submitTime%5B1%5D=2025-02-28"
        self.assertEqual(url3, expected3)


def run_manual_tests():
    """运行手动测试示例"""
    print("=" * 60)
    print("手动测试示例")
    print("=" * 60)
    
    test_cases = [
        "1",        # 单个月份
        "3,4,5",    # 多个连续月份
        "12",       # 12月
        "2",        # 2月（平年28天）
        "10 11 12", # 空格分隔
    ]
    
    for test_input in test_cases:
        try:
            print(f"\n测试输入: {test_input}")
            months = parse_month_input(test_input)
            validate_months(months)
            url = generate_url(months)
            print(f"解析结果: {months}")
            print(f"生成URL: {url}")
        except Exception as e:
            print(f"错误: {e}")


if __name__ == "__main__":
    # 运行单元测试
    print("运行单元测试...")
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # 运行手动测试
    run_manual_tests()
