#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
导出等待逻辑测试脚本
版本: v1.0.0
创建日期: 2025-07-31
变更记录:
- v1.0.0 (2025-07-31): 初始版本，测试优化后的导出等待逻辑
"""

import asyncio
import sys
from browser_controller import BrowserController
from export_handler import ExportHandler
from utils import setup_logger, print_colored

logger = setup_logger(__name__)

async def test_export_wait_logic():
    """测试导出等待逻辑"""
    browser_controller = None
    
    try:
        print_colored("🧪 开始测试导出等待逻辑", "cyan")
        print_colored("=" * 60, "cyan")
        
        # 初始化浏览器控制器
        browser_controller = BrowserController()
        
        # 连接到Chrome浏览器
        print_colored("连接到Chrome浏览器...", "blue")
        if not await browser_controller.connect_to_existing_chrome():
            print_colored("❌ 无法连接到Chrome浏览器", "red")
            print_colored("请确保Chrome已启动并开启远程调试模式", "yellow")
            return False
        
        print_colored("✅ 成功连接到Chrome浏览器", "green")
        
        # 初始化导出处理器
        export_handler = ExportHandler(browser_controller)
        
        # 测试各种检测方法
        print_colored("\n🔍 测试检测方法...", "blue")
        
        # 1. 测试导出成功消息检测
        print_colored("1. 测试导出成功消息检测", "white")
        success_detected = await export_handler.check_export_success_message()
        print_colored(f"   结果: {'✅ 检测到成功消息' if success_detected else 'ℹ️  未检测到成功消息'}", 
                     "green" if success_detected else "white")
        
        # 2. 测试导出完成可能性检测
        print_colored("2. 测试导出完成可能性检测", "white")
        likely_completed = await export_handler.check_if_export_likely_completed()
        print_colored(f"   结果: {'✅ 可能已完成' if likely_completed else 'ℹ️  可能仍在进行'}", 
                     "green" if likely_completed else "white")
        
        # 3. 显示当前页面状态
        print_colored("\n📄 当前页面状态:", "blue")
        try:
            page_title = await browser_controller.page.title()
            page_url = browser_controller.page.url
            print_colored(f"页面标题: {page_title}", "white")
            print_colored(f"页面URL: {page_url}", "white")
            
            # 检查页面内容中是否包含导出相关文本
            page_content = await browser_controller.page.text_content('body')
            export_keywords = ["导出", "下载", "成功", "完成", "中心"]
            found_keywords = [kw for kw in export_keywords if kw in page_content]
            if found_keywords:
                print_colored(f"页面包含关键词: {', '.join(found_keywords)}", "green")
            else:
                print_colored("页面未包含导出相关关键词", "white")
                
        except Exception as e:
            print_colored(f"获取页面信息失败: {e}", "red")
        
        # 4. 测试选择器
        print_colored("\n🔧 测试选择器:", "blue")
        selectors_to_test = [
            ("success_message", "成功消息"),
            ("export_success_text", "导出成功文本"),
            ("loading_indicator", "加载指示器"),
            ("error_message", "错误消息")
        ]
        
        for selector_key, description in selectors_to_test:
            try:
                selector = export_handler.selectors[selector_key]
                elements = await browser_controller.page.query_selector_all(selector)
                count = len(elements)
                if count > 0:
                    print_colored(f"  {description}: 找到 {count} 个元素", "green")
                    # 显示元素内容
                    for i, element in enumerate(elements[:3]):  # 最多显示3个
                        try:
                            text = await element.text_content()
                            if text and text.strip():
                                print_colored(f"    元素{i+1}: {text.strip()[:50]}...", "white")
                        except:
                            pass
                else:
                    print_colored(f"  {description}: 未找到元素", "white")
            except Exception as e:
                print_colored(f"  {description}: 测试失败 - {e}", "red")
        
        # 5. 模拟短时间等待测试
        print_colored("\n⏱️  模拟短时间等待测试 (10秒):", "blue")
        print_colored("这将测试新的等待逻辑是否能快速检测到完成状态", "white")
        
        try:
            # 使用较短的超时时间测试
            result = await export_handler.wait_for_export_completion(timeout=10)
            if result:
                print_colored("✅ 等待测试成功 - 检测到完成状态", "green")
            else:
                print_colored("ℹ️  等待测试超时 - 这是正常的，除非页面正在导出", "yellow")
        except Exception as e:
            print_colored(f"等待测试出错: {e}", "red")
        
        print_colored("\n✅ 导出等待逻辑测试完成", "green")
        return True
        
    except Exception as e:
        print_colored(f"❌ 测试过程中出现错误: {e}", "red")
        logger.error(f"测试过程中出现错误: {e}")
        return False
        
    finally:
        if browser_controller:
            try:
                await browser_controller.close()
            except Exception as e:
                logger.error(f"关闭浏览器时出错: {e}")

async def main():
    """主函数"""
    print_colored("🚀 导出等待逻辑测试工具", "cyan")
    print_colored("此工具用于测试优化后的导出等待和检测逻辑", "white")
    print_colored("请确保Chrome浏览器已启动并开启远程调试模式", "yellow")
    print_colored("", "white")
    
    try:
        success = await test_export_wait_logic()
        
        if success:
            print_colored("\n🎉 测试成功完成！", "green")
            return 0
        else:
            print_colored("\n❌ 测试失败", "red")
            return 1
            
    except KeyboardInterrupt:
        print_colored("\n⚠️ 测试被用户中断", "yellow")
        return 1
    except Exception as e:
        print_colored(f"\n❌ 测试异常退出: {e}", "red")
        logger.error(f"测试异常退出: {e}")
        return 1

if __name__ == "__main__":
    # 运行测试
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
