# 版本号: v1.1.0
# 变更记录:
# - v1.0.0: 初始版本，实现浏览器连接和控制功能
# - v1.1.0: 添加自动登录功能，集成Cookie管理器

"""
浏览器控制器模块
负责连接已登录的Chrome浏览器实例并进行页面操作
"""

import asyncio
from playwright.async_api import async_playwright, Browser, Page, BrowserContext
from typing import Optional
import json
from urllib.parse import urlparse

from utils import setup_logger, async_random_delay, print_colored
from cookie_manager import CookieManager
import config

logger = setup_logger(__name__)

class BrowserController:
    """浏览器控制器类"""
    
    def __init__(self):
        """初始化浏览器控制器"""
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None
        self.playwright = None
        self.logger = logger
        self.cookie_manager = CookieManager()
        
    async def connect_to_existing_chrome(self) -> bool:
        """
        连接到已存在的Chrome浏览器实例
        
        Returns:
            连接是否成功
        """
        try:
            self.playwright = await async_playwright().start()
            
            # 连接到现有的Chrome浏览器实例
            self.browser = await self.playwright.chromium.connect_over_cdp(
                config.CHROME_DEBUG_URL
            )
            
            # 获取现有的浏览器上下文
            contexts = self.browser.contexts
            if contexts:
                self.context = contexts[0]
            else:
                self.context = await self.browser.new_context()
            
            # 获取现有页面或创建新页面
            pages = self.context.pages
            if pages:
                self.page = pages[0]
            else:
                self.page = await self.context.new_page()
            
            print_colored("✓ 成功连接到Chrome浏览器", "green")
            self.logger.info("成功连接到Chrome浏览器")
            return True
            
        except Exception as e:
            print_colored(f"✗ 连接Chrome浏览器失败: {e}", "red")
            self.logger.error(f"连接Chrome浏览器失败: {e}")
            return False
    
    async def navigate_to_target_page(self, url: str) -> bool:
        """
        导航到目标页面
        
        Args:
            url: 目标URL
            
        Returns:
            导航是否成功
        """
        try:
            if not self.page:
                raise Exception("浏览器页面未初始化")
            
            print_colored(f"正在导航到: {url}", "blue")
            await self.page.goto(url, wait_until="networkidle")
            
            # 等待页面加载完成
            await async_random_delay(config.MIN_DELAY, config.MAX_DELAY)
            
            current_url = self.page.url
            print_colored(f"✓ 成功导航到页面: {current_url}", "green")
            self.logger.info(f"成功导航到页面: {current_url}")
            return True
            
        except Exception as e:
            print_colored(f"✗ 导航失败: {e}", "red")
            self.logger.error(f"导航失败: {e}")
            return False
    
    async def wait_for_element(self, selector: str, timeout: int = 30000) -> bool:
        """
        等待元素出现
        
        Args:
            selector: 元素选择器
            timeout: 超时时间（毫秒）
            
        Returns:
            元素是否出现
        """
        try:
            if not self.page:
                raise Exception("浏览器页面未初始化")
            
            await self.page.wait_for_selector(selector, timeout=timeout)
            self.logger.info(f"元素已出现: {selector}")
            return True
            
        except Exception as e:
            self.logger.error(f"等待元素失败: {selector}, 错误: {e}")
            return False
    
    async def click_element(self, selector: str) -> bool:
        """
        点击页面元素
        
        Args:
            selector: 元素选择器
            
        Returns:
            点击是否成功
        """
        try:
            if not self.page:
                raise Exception("浏览器页面未初始化")
            
            await self.page.click(selector)
            await async_random_delay(1, 2)  # 点击后短暂延迟
            
            self.logger.info(f"成功点击元素: {selector}")
            return True
            
        except Exception as e:
            self.logger.error(f"点击元素失败: {selector}, 错误: {e}")
            return False
    
    async def fill_input(self, selector: str, value: str) -> bool:
        """
        填充输入框
        
        Args:
            selector: 输入框选择器
            value: 要填充的值
            
        Returns:
            填充是否成功
        """
        try:
            if not self.page:
                raise Exception("浏览器页面未初始化")
            
            await self.page.fill(selector, value)
            await async_random_delay(0.5, 1)  # 填充后短暂延迟
            
            self.logger.info(f"成功填充输入框: {selector} = {value}")
            return True
            
        except Exception as e:
            self.logger.error(f"填充输入框失败: {selector}, 错误: {e}")
            return False
    
    async def get_page_title(self) -> str:
        """
        获取页面标题
        
        Returns:
            页面标题
        """
        try:
            if not self.page:
                return ""
            
            title = await self.page.title()
            return title
            
        except Exception as e:
            self.logger.error(f"获取页面标题失败: {e}")
            return ""
    
    async def extract_and_save_cookies(self, domain: str = None) -> bool:
        """
        提取并保存当前浏览器的cookies

        Args:
            domain: 指定域名，如果为None则提取所有cookies

        Returns:
            操作是否成功
        """
        try:
            if not self.context:
                raise Exception("浏览器上下文未初始化")

            # 如果没有指定域名，从目标URL中提取
            if not domain and hasattr(config, 'TARGET_URL'):
                parsed_url = urlparse(config.TARGET_URL)
                domain = parsed_url.netloc

            # 提取cookies
            cookies = await self.cookie_manager.extract_cookies(self.context, domain)

            if not cookies:
                print_colored("✗ 没有找到可用的cookies", "yellow")
                return False

            # 保存cookies
            additional_info = {
                "domain": domain,
                "url": self.page.url if self.page else "",
                "user_agent": await self.page.evaluate("navigator.userAgent") if self.page else ""
            }

            return self.cookie_manager.save_cookies(cookies, additional_info)

        except Exception as e:
            print_colored(f"✗ 提取并保存cookies失败: {e}", "red")
            self.logger.error(f"提取并保存cookies失败: {e}")
            return False

    async def load_and_apply_cookies(self) -> bool:
        """
        加载并应用保存的cookies

        Returns:
            操作是否成功
        """
        try:
            if not self.context:
                raise Exception("浏览器上下文未初始化")

            # 加载cookies
            cookies = self.cookie_manager.load_cookies()

            if not cookies:
                print_colored("✗ 没有可用的cookies", "yellow")
                return False

            # 应用cookies
            return await self.cookie_manager.apply_cookies(self.context, cookies)

        except Exception as e:
            print_colored(f"✗ 加载并应用cookies失败: {e}", "red")
            self.logger.error(f"加载并应用cookies失败: {e}")
            return False

    async def auto_login_with_cookies(self, target_url: str = None,
                                    login_check_selector: str = None) -> bool:
        """
        使用cookies自动登录

        Args:
            target_url: 目标URL，如果为None则使用配置中的URL
            login_check_selector: 登录状态检查选择器

        Returns:
            自动登录是否成功
        """
        try:
            print_colored("开始尝试自动登录...", "blue")

            # 使用默认URL
            if not target_url:
                target_url = config.TARGET_URL

            # 先加载并应用cookies
            cookie_applied = await self.load_and_apply_cookies()

            if not cookie_applied:
                print_colored("✗ 无法应用cookies，需要手动登录", "yellow")
                return False

            # 导航到目标页面
            success = await self.navigate_to_target_page(target_url)
            if not success:
                return False

            # 检查登录状态
            is_logged_in = await self.cookie_manager.check_login_status(
                self.page, target_url, login_check_selector
            )

            if is_logged_in:
                print_colored("✓ 自动登录成功！", "green")
                self.logger.info("自动登录成功")
                return True
            else:
                print_colored("✗ 自动登录失败，可能需要重新登录", "yellow")
                self.logger.warning("自动登录失败")
                return False

        except Exception as e:
            print_colored(f"✗ 自动登录过程失败: {e}", "red")
            self.logger.error(f"自动登录过程失败: {e}")
            return False

    def get_cookie_status(self) -> dict:
        """
        获取cookie状态信息

        Returns:
            cookie状态字典
        """
        return self.cookie_manager.get_cookie_info()

    def delete_saved_cookies(self) -> bool:
        """
        删除保存的cookies

        Returns:
            删除是否成功
        """
        return self.cookie_manager.delete_cookies()

    async def close(self):
        """关闭浏览器连接"""
        try:
            if self.playwright:
                await self.playwright.stop()
                print_colored("✓ 浏览器连接已关闭", "yellow")
                self.logger.info("浏览器连接已关闭")

        except Exception as e:
            self.logger.error(f"关闭浏览器连接失败: {e}")
