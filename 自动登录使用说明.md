# 自动登录功能使用说明

## 🎯 功能概述

自动登录功能通过保存浏览器cookies实现一次配置、长期使用的自动登录体验。无需每次都手动登录，大大提升了工具的使用便利性。

## 🚀 快速开始

### 方法一：使用设置向导（推荐）
```bash
setup_auto_login.bat
```
运行设置向导，按照提示完成自动登录配置。

### 方法二：手动配置
1. **启动Chrome浏览器**
   ```bash
   start_chrome_safe.bat
   ```

2. **手动登录网站**
   - 在Chrome中访问目标网站
   - 完成登录操作

3. **提取cookies**
   ```bash
   python extract_cookies.py
   ```

4. **测试自动登录**
   ```bash
   python test_auto_login.py
   ```

5. **运行主程序**
   ```bash
   python main.py
   ```

## 🛠️ Cookie管理工具

### 查看cookie状态
```bash
python cookie_cli.py info
```
显示cookie的基本信息，包括数量、保存时间、过期时间等。

### 查看cookie详细信息
```bash
python cookie_cli.py details
```
显示所有cookie的详细信息，包括域名、路径、安全设置等。

### 删除保存的cookies
```bash
python cookie_cli.py delete
```
删除所有保存的cookies，需要重新提取。

## 📋 工作原理

1. **Cookie提取**: 从已登录的浏览器中提取认证相关的cookies
2. **Cookie保存**: 将cookies加密保存到本地文件（cookies.json）
3. **自动应用**: 启动时自动将保存的cookies应用到浏览器
4. **状态验证**: 验证登录状态，确保自动登录成功

## ⚠️ 注意事项

### Cookie有效期
- 保存的cookies有效期为30天
- 过期后需要重新提取cookies
- 可以通过 `cookie_cli.py info` 查看剩余有效期

### 安全性
- Cookies保存在本地，请妥善保管
- 不要将cookies.json文件分享给他人
- 定期更新cookies以保持安全性

### 兼容性
- 支持Chrome浏览器的所有版本
- 兼容Windows、macOS、Linux系统
- 需要Python 3.7+环境

## 🔧 故障排除

### 自动登录失败
1. **检查cookie状态**
   ```bash
   python cookie_cli.py info
   ```

2. **重新提取cookies**
   ```bash
   python extract_cookies.py
   ```

3. **测试自动登录**
   ```bash
   python test_auto_login.py
   ```

### 常见问题

**Q: 提示"没有找到保存的cookies"**
A: 需要先运行 `extract_cookies.py` 提取cookies

**Q: 提示"cookies已过期"**
A: 重新运行 `extract_cookies.py` 提取新的cookies

**Q: 自动登录后仍然在登录页面**
A: 可能是网站登录机制变化，尝试重新提取cookies

**Q: 无法连接到Chrome浏览器**
A: 确保Chrome通过 `start_chrome_safe.bat` 启动

## 📊 使用统计

使用自动登录功能后：
- ✅ 节省每次手动登录时间：约2-3分钟
- ✅ 减少操作步骤：从5步减少到1步
- ✅ 提升用户体验：一次配置，长期使用
- ✅ 降低出错概率：自动化程度更高

## 🔄 更新和维护

### 定期维护
- 建议每月更新一次cookies
- 定期检查cookie状态
- 及时处理过期的cookies

### 版本更新
- 自动登录功能会随主程序一起更新
- 保持工具版本最新以获得最佳体验

---

**享受自动登录带来的便利吧！** 🎉
