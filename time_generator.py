# 版本号: v1.0.0
# 变更记录: 
# - v1.0.0: 初始版本，实现时间范围生成功能

"""
时间范围生成器模块
负责生成按指定月份数量分组的时间范围列表
"""

from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
from typing import List, Tuple
import calendar

from utils import setup_logger

logger = setup_logger(__name__)

class TimeRangeGenerator:
    """时间范围生成器类"""
    
    def __init__(self, year: int, months_per_range: int = 2):
        """
        初始化时间范围生成器
        
        Args:
            year: 目标年份
            months_per_range: 每个时间范围包含的月份数
        """
        self.year = year
        self.months_per_range = months_per_range
        self.logger = logger
        
    def generate_time_ranges(self) -> List[Tuple[str, str]]:
        """
        生成全年的时间范围列表
        
        Returns:
            时间范围元组列表，每个元组包含(开始日期, 结束日期)
        """
        time_ranges = []
        current_month = 1
        
        while current_month <= 12:
            start_date, end_date = self._generate_single_range(current_month)
            time_ranges.append((start_date, end_date))
            
            self.logger.info(f"生成时间范围: {start_date} 至 {end_date}")
            current_month += self.months_per_range
            
        return time_ranges
    
    def _generate_single_range(self, start_month: int) -> Tuple[str, str]:
        """
        生成单个时间范围
        
        Args:
            start_month: 起始月份
            
        Returns:
            (开始日期, 结束日期) 元组
        """
        # 计算结束月份，确保不超过12月
        end_month = min(start_month + self.months_per_range - 1, 12)
        
        # 生成开始日期（月份第一天）
        start_date = datetime(self.year, start_month, 1)
        
        # 生成结束日期（月份最后一天）
        last_day = calendar.monthrange(self.year, end_month)[1]
        end_date = datetime(self.year, end_month, last_day)
        
        # 格式化为字符串
        start_str = start_date.strftime("%Y-%m-%d")
        end_str = end_date.strftime("%Y-%m-%d")
        
        return start_str, end_str
    
    def get_range_description(self, start_date: str, end_date: str) -> str:
        """
        获取时间范围的描述信息
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            时间范围描述字符串
        """
        start_dt = datetime.strptime(start_date, "%Y-%m-%d")
        end_dt = datetime.strptime(end_date, "%Y-%m-%d")
        
        start_month_name = start_dt.strftime("%m月")
        end_month_name = end_dt.strftime("%m月")
        
        if start_month_name == end_month_name:
            return f"{self.year}年{start_month_name}"
        else:
            return f"{self.year}年{start_month_name}至{end_month_name}"
    
    def validate_date_range(self, start_date: str, end_date: str) -> bool:
        """
        验证日期范围是否有效
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            是否有效
        """
        try:
            start_dt = datetime.strptime(start_date, "%Y-%m-%d")
            end_dt = datetime.strptime(end_date, "%Y-%m-%d")
            
            # 检查日期顺序
            if start_dt > end_dt:
                return False
                
            # 检查时间跨度是否超过限制（3个月）
            months_diff = (end_dt.year - start_dt.year) * 12 + end_dt.month - start_dt.month
            if months_diff >= 3:
                self.logger.warning(f"时间范围超过3个月限制: {months_diff + 1}个月")
                return False
                
            return True
            
        except ValueError as e:
            self.logger.error(f"日期格式错误: {e}")
            return False
