# 版本号: v1.0.0
# 变更记录: 
# - v1.0.0: 初始版本，实现cookie管理命令行工具

"""
Cookie管理命令行工具
提供cookie的查看、删除等管理功能
"""

import sys
import json
from datetime import datetime

from cookie_manager import CookieManager
from utils import print_colored

def show_cookie_info():
    """显示cookie信息"""
    cookie_manager = CookieManager()
    info = cookie_manager.get_cookie_info()
    
    print_colored("=== Cookie信息 ===", "cyan")
    
    if not info.get("exists"):
        print_colored("✗ 没有找到保存的cookies", "red")
        if info.get("error"):
            print_colored(f"错误: {info['error']}", "red")
        return
    
    print_colored("✓ 找到保存的cookies", "green")
    print_colored(f"Cookie数量: {info.get('cookie_count', 0)}", "blue")
    print_colored(f"保存时间: {info.get('saved_at', 'N/A')}", "blue")
    print_colored(f"过期时间: {info.get('expires_at', 'N/A')}", "blue")
    
    if info.get('is_expired'):
        print_colored("状态: 已过期", "red")
    else:
        days_left = info.get('days_until_expiry', 0)
        print_colored(f"状态: 有效 (剩余 {days_left} 天)", "green")

def show_cookie_details():
    """显示cookie详细信息"""
    cookie_manager = CookieManager()
    
    try:
        with open(cookie_manager.cookie_file, 'r', encoding='utf-8') as f:
            cookie_data = json.load(f)
        
        print_colored("=== Cookie详细信息 ===", "cyan")
        
        # 基本信息
        print_colored(f"保存时间: {cookie_data.get('saved_at', 'N/A')}", "blue")
        print_colored(f"过期时间: {cookie_data.get('expires_at', 'N/A')}", "blue")
        
        # 额外信息
        additional_info = cookie_data.get('additional_info', {})
        if additional_info:
            print_colored("\n额外信息:", "yellow")
            for key, value in additional_info.items():
                print_colored(f"  {key}: {value}", "white")
        
        # Cookie列表
        cookies = cookie_data.get('cookies', [])
        print_colored(f"\nCookie列表 (共 {len(cookies)} 个):", "yellow")
        
        for i, cookie in enumerate(cookies, 1):
            name = cookie.get('name', 'N/A')
            domain = cookie.get('domain', 'N/A')
            path = cookie.get('path', 'N/A')
            secure = cookie.get('secure', False)
            http_only = cookie.get('httpOnly', False)
            
            print_colored(f"  {i}. {name}", "white")
            print_colored(f"     域名: {domain}", "gray")
            print_colored(f"     路径: {path}", "gray")
            print_colored(f"     安全: {'是' if secure else '否'}", "gray")
            print_colored(f"     HTTP Only: {'是' if http_only else '否'}", "gray")
            
            if i >= 10:  # 只显示前10个
                remaining = len(cookies) - 10
                if remaining > 0:
                    print_colored(f"  ... 还有 {remaining} 个cookies", "gray")
                break
                
    except FileNotFoundError:
        print_colored("✗ 没有找到cookie文件", "red")
    except Exception as e:
        print_colored(f"✗ 读取cookie文件失败: {e}", "red")

def delete_cookies():
    """删除cookies"""
    cookie_manager = CookieManager()
    
    # 先显示当前状态
    info = cookie_manager.get_cookie_info()
    if not info.get("exists"):
        print_colored("✗ 没有找到保存的cookies", "yellow")
        return
    
    print_colored("当前cookie信息:", "blue")
    print_colored(f"  Cookie数量: {info.get('cookie_count', 0)}", "blue")
    print_colored(f"  保存时间: {info.get('saved_at', 'N/A')}", "blue")
    
    # 确认删除
    print_colored("\n确定要删除所有保存的cookies吗？(y/N): ", "yellow", end="")
    confirm = input().strip().lower()
    
    if confirm in ['y', 'yes', '是']:
        success = cookie_manager.delete_cookies()
        if success:
            print_colored("✓ Cookies已删除", "green")
        else:
            print_colored("✗ 删除cookies失败", "red")
    else:
        print_colored("取消删除操作", "yellow")

def show_usage():
    """显示使用说明"""
    print_colored("=== Cookie管理工具 ===", "cyan")
    print()
    print_colored("用法: python cookie_cli.py [命令]", "blue")
    print()
    print_colored("可用命令:", "yellow")
    print_colored("  info     - 显示cookie基本信息", "white")
    print_colored("  details  - 显示cookie详细信息", "white")
    print_colored("  delete   - 删除保存的cookies", "white")
    print_colored("  help     - 显示此帮助信息", "white")
    print()
    print_colored("示例:", "yellow")
    print_colored("  python cookie_cli.py info", "white")
    print_colored("  python cookie_cli.py details", "white")
    print_colored("  python cookie_cli.py delete", "white")
    print()

def main():
    """主函数"""
    if len(sys.argv) < 2:
        show_usage()
        return 0
    
    command = sys.argv[1].lower()
    
    if command in ['help', '-h', '--help']:
        show_usage()
    elif command == 'info':
        show_cookie_info()
    elif command == 'details':
        show_cookie_details()
    elif command == 'delete':
        delete_cookies()
    else:
        print_colored(f"✗ 未知命令: {command}", "red")
        print_colored("使用 'python cookie_cli.py help' 查看帮助", "yellow")
        return 1
    
    return 0

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print_colored("\n用户取消操作", "yellow")
        sys.exit(1)
    except Exception as e:
        print_colored(f"\n程序异常退出: {e}", "red")
        sys.exit(1)
