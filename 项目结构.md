# 自动化数据导出工具 - 项目结构

## 📁 项目文件说明

### 🚀 启动文件
- **`start.bat`** - 主启动脚本，提供多种启动选项
- **`start_chrome_safe.bat`** - 安全Chrome启动脚本（无安全警告）
- **`setup_auto_login.bat`** - 自动登录设置向导

### 🔧 核心程序文件
- **`main.py`** - 主程序入口，协调整个导出流程（支持自动登录）
- **`browser_controller.py`** - 浏览器控制器，处理Chrome连接和页面操作
- **`export_handler.py`** - 导出处理器，处理具体的导出操作流程
- **`time_generator.py`** - 时间范围生成器，生成12个月的时间段
- **`utils.py`** - 通用工具函数，提供日志、延迟等功能
- **`config.py`** - 配置文件，包含所有可调整的参数

### 🍪 自动登录模块
- **`cookie_manager.py`** - Cookie管理器，处理cookie的提取、保存和应用
- **`extract_cookies.py`** - Cookie提取工具，从已登录浏览器提取cookies
- **`test_auto_login.py`** - 自动登录测试工具，验证cookie自动登录功能
- **`cookie_cli.py`** - Cookie管理命令行工具，提供cookie管理功能

### 📦 依赖和文档
- **`requirements.txt`** - Python依赖包列表
- **`README.md`** - 主要使用说明文档
- **`Chrome安全启动.md`** - Chrome安全启动专门说明

## 🎯 使用流程

### 方式一：自动登录（推荐）
1. **安装依赖**: `pip install -r requirements.txt`
2. **设置自动登录**: 运行 `setup_auto_login.bat`
3. **运行工具**: 执行 `python main.py`

### 方式二：手动登录
1. **安装依赖**: `pip install -r requirements.txt`
2. **启动Chrome**: 运行 `start_chrome_safe.bat`
3. **登录网站**: 在Chrome中完成登录
4. **运行工具**: 执行 `python main.py`

## 📊 功能特性

- ✅ **12个月自动导出** - 2025年1月至12月
- ✅ **完整操作流程** - 设置日期→搜索→导出业务数据
- ✅ **自动登录功能** - 一次配置，长期使用
- ✅ **Cookie管理** - 完整的cookie管理工具链
- ✅ **安全Chrome启动** - 无安全警告
- ✅ **智能错误重试** - 自动重试失败的操作
- ✅ **详细进度显示** - 实时显示操作状态

## 🔒 安全性

- 移除了不安全的Chrome启动参数
- 保持完整的网页安全性
- 使用独立的用户数据目录

---

**项目已优化完成，文件结构简洁明了！**
