# 版本号: v1.0.0
# 变更记录: 
# - v1.0.0: 初始版本，实现cookie提取、保存和自动登录功能

"""
Cookie管理器模块
负责提取、保存和加载浏览器cookie，实现自动登录功能
"""

import json
import os
from typing import List, Dict, Optional
from playwright.async_api import BrowserContext, Page
from datetime import datetime, timedelta

from utils import setup_logger, print_colored
import config

logger = setup_logger(__name__)

class CookieManager:
    """Cookie管理器类"""
    
    def __init__(self, cookie_file: str = "cookies.json"):
        """
        初始化Cookie管理器
        
        Args:
            cookie_file: cookie保存文件路径
        """
        self.cookie_file = cookie_file
        self.logger = logger
        
    async def extract_cookies(self, context: BrowserContext, domain: str = None) -> List[Dict]:
        """
        从浏览器上下文中提取cookies

        Args:
            context: 浏览器上下文
            domain: 指定域名，如果为None则提取所有cookies

        Returns:
            cookies列表
        """
        try:
            all_cookies = await context.cookies()

            if domain:
                # 过滤指定域名及其子域名的cookies
                filtered_cookies = []
                for cookie in all_cookies:
                    cookie_domain = cookie.get('domain', '')
                    # 包含主域名和子域名的cookies
                    if (domain in cookie_domain or
                        cookie_domain.endswith('.' + domain) or
                        cookie_domain.startswith('.')):
                        filtered_cookies.append(cookie)
                cookies = filtered_cookies
            else:
                cookies = all_cookies

            # 显示提取的cookie信息
            print_colored(f"✓ 成功提取 {len(cookies)} 个cookies", "green")

            # 显示cookie名称以便调试
            if cookies:
                cookie_names = [cookie.get('name', 'unknown') for cookie in cookies]
                print_colored(f"Cookie名称: {', '.join(cookie_names)}", "blue")

            self.logger.info(f"成功提取 {len(cookies)} 个cookies: {[c.get('name') for c in cookies]}")

            return cookies

        except Exception as e:
            print_colored(f"✗ 提取cookies失败: {e}", "red")
            self.logger.error(f"提取cookies失败: {e}")
            return []
    
    def save_cookies(self, cookies: List[Dict], additional_info: Dict = None) -> bool:
        """
        保存cookies到文件
        
        Args:
            cookies: cookies列表
            additional_info: 额外信息（如保存时间等）
            
        Returns:
            保存是否成功
        """
        try:
            cookie_data = {
                "cookies": cookies,
                "saved_at": datetime.now().isoformat(),
                "expires_at": (datetime.now() + timedelta(days=30)).isoformat(),  # 30天后过期
                "additional_info": additional_info or {}
            }
            
            with open(self.cookie_file, 'w', encoding='utf-8') as f:
                json.dump(cookie_data, f, indent=2, ensure_ascii=False)
            
            print_colored(f"✓ cookies已保存到: {self.cookie_file}", "green")
            self.logger.info(f"cookies已保存到: {self.cookie_file}")
            return True
            
        except Exception as e:
            print_colored(f"✗ 保存cookies失败: {e}", "red")
            self.logger.error(f"保存cookies失败: {e}")
            return False
    
    def load_cookies(self) -> Optional[List[Dict]]:
        """
        从文件加载cookies
        
        Returns:
            cookies列表，如果加载失败或过期则返回None
        """
        try:
            if not os.path.exists(self.cookie_file):
                print_colored("✗ cookies文件不存在", "yellow")
                return None
            
            with open(self.cookie_file, 'r', encoding='utf-8') as f:
                cookie_data = json.load(f)
            
            # 检查是否过期
            expires_at = datetime.fromisoformat(cookie_data.get('expires_at', ''))
            if datetime.now() > expires_at:
                print_colored("✗ cookies已过期", "yellow")
                self.logger.warning("cookies已过期")
                return None
            
            cookies = cookie_data.get('cookies', [])
            saved_at = cookie_data.get('saved_at', '')
            
            print_colored(f"✓ 成功加载 {len(cookies)} 个cookies (保存于: {saved_at})", "green")
            self.logger.info(f"成功加载 {len(cookies)} 个cookies")
            
            return cookies
            
        except Exception as e:
            print_colored(f"✗ 加载cookies失败: {e}", "red")
            self.logger.error(f"加载cookies失败: {e}")
            return None
    
    async def apply_cookies(self, context: BrowserContext, cookies: List[Dict]) -> bool:
        """
        将cookies应用到浏览器上下文
        
        Args:
            context: 浏览器上下文
            cookies: cookies列表
            
        Returns:
            应用是否成功
        """
        try:
            await context.add_cookies(cookies)
            print_colored(f"✓ 成功应用 {len(cookies)} 个cookies", "green")
            self.logger.info(f"成功应用 {len(cookies)} 个cookies")
            return True
            
        except Exception as e:
            print_colored(f"✗ 应用cookies失败: {e}", "red")
            self.logger.error(f"应用cookies失败: {e}")
            return False
    
    async def check_login_status(self, page: Page, login_check_url: str = None, 
                                login_indicator: str = None) -> bool:
        """
        检查登录状态
        
        Args:
            page: 页面对象
            login_check_url: 用于检查登录状态的URL
            login_indicator: 登录状态指示器（CSS选择器或文本）
            
        Returns:
            是否已登录
        """
        try:
            # 如果指定了检查URL，先导航到该URL
            if login_check_url:
                await page.goto(login_check_url, wait_until="networkidle")
            
            # 检查登录状态
            if login_indicator:
                try:
                    # 尝试查找登录指示器
                    await page.wait_for_selector(login_indicator, timeout=5000)
                    print_colored("✓ 检测到已登录状态", "green")
                    return True
                except:
                    print_colored("✗ 未检测到登录状态", "yellow")
                    return False
            else:
                # 简单检查：如果页面URL包含登录相关关键词，可能未登录
                current_url = page.url.lower()
                if any(keyword in current_url for keyword in ['login', 'signin', 'auth']):
                    print_colored("✗ 页面URL显示未登录", "yellow")
                    return False
                else:
                    print_colored("✓ 页面URL显示可能已登录", "green")
                    return True
                    
        except Exception as e:
            print_colored(f"✗ 检查登录状态失败: {e}", "red")
            self.logger.error(f"检查登录状态失败: {e}")
            return False
    
    def get_cookie_info(self) -> Dict:
        """
        获取cookie文件信息
        
        Returns:
            cookie信息字典
        """
        try:
            if not os.path.exists(self.cookie_file):
                return {"exists": False}
            
            with open(self.cookie_file, 'r', encoding='utf-8') as f:
                cookie_data = json.load(f)
            
            saved_at = datetime.fromisoformat(cookie_data.get('saved_at', ''))
            expires_at = datetime.fromisoformat(cookie_data.get('expires_at', ''))
            is_expired = datetime.now() > expires_at
            
            return {
                "exists": True,
                "cookie_count": len(cookie_data.get('cookies', [])),
                "saved_at": saved_at.strftime("%Y-%m-%d %H:%M:%S"),
                "expires_at": expires_at.strftime("%Y-%m-%d %H:%M:%S"),
                "is_expired": is_expired,
                "days_until_expiry": (expires_at - datetime.now()).days if not is_expired else 0
            }
            
        except Exception as e:
            self.logger.error(f"获取cookie信息失败: {e}")
            return {"exists": False, "error": str(e)}
    
    def delete_cookies(self) -> bool:
        """
        删除cookie文件
        
        Returns:
            删除是否成功
        """
        try:
            if os.path.exists(self.cookie_file):
                os.remove(self.cookie_file)
                print_colored(f"✓ 已删除cookie文件: {self.cookie_file}", "green")
                return True
            else:
                print_colored("✗ cookie文件不存在", "yellow")
                return False
                
        except Exception as e:
            print_colored(f"✗ 删除cookie文件失败: {e}", "red")
            self.logger.error(f"删除cookie文件失败: {e}")
            return False
