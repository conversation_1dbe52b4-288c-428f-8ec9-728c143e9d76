# 简化导出工具使用说明

## 版本信息
- **版本号**: v1.0.0
- **创建日期**: 2025-07-31
- **适用系统**: Windows
- **依赖**: Chrome浏览器 + 远程调试模式

## 功能概述

简化导出工具是一个通过直接URL访问实现自动化数据导出的工具，旨在替代复杂的页面操作，提升导出效率和用户体验。

### 主要特性

1. **直接URL访问**: 绕过复杂的页面元素操作，直接通过URL参数指定时间范围
2. **月份范围选择**: 支持用户输入开始和结束月份（1-12），自动生成对应的URL
3. **自动化流程**: 依次访问每个URL，自动执行搜索和导出操作
4. **进度显示**: 实时显示处理进度和状态信息
5. **错误处理**: 全面的错误捕获和用户友好的错误提示

## 使用前准备

### 1. 启动Chrome浏览器（调试模式）

```bash
chrome.exe --remote-debugging-port=9222
```

或者使用项目提供的批处理文件：
- `start_chrome_safe.bat`
- `启动工具集.bat`

### 2. 确保网络连接稳定

确保能够正常访问目标网站：
```
https://tastien.tastientech.com/portal/expand-store/developStores/bunk
```

### 3. 配置自动登录（推荐）

首次使用建议先配置自动登录：
1. 运行主程序：`python main.py`
2. 选择选项3：Cookie管理工具
3. 提取并保存Cookie信息

## 使用步骤

### 1. 启动工具

```bash
python main.py
```

### 2. 选择简化导出工具

在主菜单中选择选项 **4. 🚀 简化导出工具**

### 3. 输入月份范围

- **开始月份**: 输入1-12之间的数字
- **结束月份**: 输入1-12之间的数字（必须≥开始月份）

#### 示例输入
```
请输入开始月份 (1-12): 1
请输入结束月份 (1-12): 12
```

### 4. 确认导出

系统会显示生成的URL列表和预计时间，确认后开始执行。

### 5. 监控进度

工具会自动执行以下流程：
1. 访问URL
2. 点击搜索按钮
3. 等待3秒
4. 点击导出业务按钮
5. 等待3秒
6. 继续下一个URL

## URL生成规则

### 基础URL模板
```
https://tastien.tastientech.com/portal/expand-store/developStores/bunk?current=1&pageSize=10&submitTime%5B0%5D={开始日期}&submitTime%5B1%5D={结束日期}
```

### 日期格式
- **格式**: YYYY-MM-DD
- **年份**: 固定为2025年
- **日期范围**: 每月从1号到该月最后一天

### 月份天数规则
- **31天月份**: 1, 3, 5, 7, 8, 10, 12月
- **30天月份**: 4, 6, 9, 11月
- **28天月份**: 2月（2025年非闰年）

## 使用示例

### 示例1: 单月导出
```
开始月份: 3
结束月份: 3
生成URL: ...&submitTime%5B0%5D=2025-03-01&submitTime%5B1%5D=2025-03-31
```

### 示例2: 季度导出
```
开始月份: 1
结束月份: 3
生成3个URL，分别对应1月、2月、3月
```

### 示例3: 全年导出
```
开始月份: 1
结束月份: 12
生成12个URL，覆盖全年数据
```

## 错误处理

### 常见错误及解决方案

1. **浏览器连接失败**
   - 确保Chrome已启动并开启调试模式
   - 检查端口9222是否被占用

2. **页面元素找不到**
   - 检查网站页面结构是否发生变化
   - 确认已正确登录网站

3. **网络连接问题**
   - 检查网络连接是否稳定
   - 确认能够访问目标网站

4. **导出按钮点击失败**
   - 可能页面加载未完成，工具会自动重试
   - 检查是否有权限进行导出操作

## 性能说明

### 时间估算
- **单个月份**: 约30秒（包括页面加载和等待时间）
- **全年导出**: 约6分钟（12个月份）
- **实际时间**: 取决于网络速度和服务器响应

### 资源占用
- **内存**: 约100-200MB（Chrome浏览器占用）
- **CPU**: 低占用，主要是等待时间
- **网络**: 每个请求约1-5MB数据传输

## 注意事项

1. **不要关闭Chrome浏览器**: 工具运行期间保持Chrome开启
2. **避免手动操作**: 工具运行时不要手动操作浏览器页面
3. **网络稳定性**: 确保网络连接稳定，避免中途断网
4. **权限确认**: 确保账户有导出数据的权限
5. **数据备份**: 重要数据建议多次导出确认

## 故障排除

### 调试信息查看
工具会生成详细的日志信息，可以通过以下方式查看：
- 控制台输出：实时显示操作状态
- 日志文件：详细的错误信息和调试信息

### 测试工具
可以使用测试脚本验证功能：
```bash
python test_simplified_export.py
```

### 联系支持
如遇到问题，请提供：
- 错误信息截图
- 操作步骤描述
- 系统环境信息
- 日志文件内容

## 更新日志

### v1.0.0 (2025-07-31)
- 初始版本发布
- 实现基础的URL生成和自动化导出功能
- 支持月份范围选择
- 集成到主程序菜单
- 添加完整的错误处理和进度显示
